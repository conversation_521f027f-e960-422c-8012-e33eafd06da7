# ESP32-MCP-HARDWARE 项目结构说明

## 项目概述

ESP32-MCP-HARDWARE 是一个基于ESP32的硬件控制平台，专注于提供MCP通信和双屏眼睛模拟功能。项目采用分层架构设计，便于扩展新的硬件模块。

## 核心功能

1. **MCP通信** - 支持WebSocket MCP协议，可与小智AI等MCP客户端通信
2. **双屏眼睛模拟** - 基于LVGL的双GC9A01圆形显示屏眼睛效果
3. **硬件扩展** - 支持添加新的硬件模块（如舵机、传感器等）

## 项目结构

```
ESP32-MCP-HARDWARE/
├── main/                       # 主程序目录
│   ├── common/                 # 通用组件层
│   │   ├── app_config.h        # 应用配置定义
│   │   ├── wifi_manager.c      # WiFi连接管理
│   │   ├── wifi_manager.h      # WiFi管理接口
│   │   └── lv_conf.h           # LVGL配置文件
│   │
│   ├── eyes_layer/             # 眼睛显示层
│   │   ├── eye_display.c       # 眼睛显示实现
│   │   └── eye_display.h       # 眼睛显示接口
│   │
│   ├── mcp_layer/              # MCP通信层
│   │   ├── mcp_client.c        # MCP客户端实现
│   │   └── mcp_client.h        # MCP客户端接口
│   │
│   ├── main.c                  # 主程序入口
│   ├── CMakeLists.txt          # 构建配置
│   └── Kconfig.projbuild       # 配置选项
│
├── CMakeLists.txt              # 项目构建文件
├── sdkconfig.defaults          # 默认配置
├── build.sh                    # 构建脚本
├── example_config.txt          # 配置示例
├── test_mcp_tools.py           # MCP工具测试脚本
└── readme.md                   # 项目说明文档
```

## 分层架构说明

### Common层 (通用组件)
- **作用**: 提供项目通用的基础功能
- **包含**: WiFi管理、应用配置、LVGL配置
- **特点**: 被其他层级共享使用

### Eyes_layer (眼睛显示层)
- **作用**: 负责双屏眼睛显示功能
- **包含**: LVGL眼睛动画、GC9A01驱动、显示管理
- **特点**: 独立的硬件功能模块

### MCP_layer (MCP通信层)
- **作用**: 处理MCP协议通信
- **包含**: WebSocket客户端、工具注册、消息处理
- **特点**: 提供标准MCP接口

## 硬件扩展指南

### 添加新硬件层

要添加新的硬件功能（如舵机控制），按以下步骤：

1. **创建硬件层目录**
   ```
   main/servo_layer/
   ├── servo_control.c     # 舵机控制实现
   └── servo_control.h     # 舵机控制接口
   ```

2. **实现硬件接口**
   - 定义硬件控制API
   - 实现硬件初始化和控制逻辑
   - 提供MCP工具接口

3. **集成到主程序**
   - 在 `main.c` 中包含新的头文件
   - 在初始化流程中调用硬件初始化
   - 注册相关的MCP工具

4. **更新构建配置**
   - 在 `CMakeLists.txt` 中添加新的源文件
   - 添加相应的包含目录

### 示例：添加舵机控制层

```c
// main/servo_layer/servo_control.h
#ifndef SERVO_CONTROL_H
#define SERVO_CONTROL_H

#include "esp_err.h"

esp_err_t servo_init(void);
esp_err_t servo_set_angle(int servo_id, float angle);
esp_err_t servo_get_angle(int servo_id, float* angle);

#endif
```

```c
// main/main.c 中添加
#include "servo_layer/servo_control.h"

// 在 app_main() 中初始化
ESP_ERROR_CHECK(servo_init());

// 注册MCP工具
static mcp_tool_response_t servo_control_tool(const char* args) {
    // 实现舵机控制逻辑
}
```

## 配置管理

项目使用ESP-IDF的Kconfig系统进行配置管理：

- **WiFi配置**: SSID、密码、重连次数
- **MCP配置**: 服务器端点、重连间隔、缓冲区大小
- **硬件配置**: 引脚定义、动画参数
- **眼睛动画配置**: 眨眼间隔、移动范围、情绪变化

## 开发建议

1. **保持分层独立**: 每个硬件层应该相对独立，减少层间耦合
2. **统一接口设计**: 新硬件层应该提供一致的初始化和控制接口
3. **错误处理**: 每个层级都应该有完善的错误处理机制
4. **资源管理**: 注意内存和任务资源的合理分配
5. **文档更新**: 添加新功能时及时更新相关文档

## 技术特点

- **模块化设计**: 按功能分层，便于维护和扩展
- **标准化接口**: 统一的初始化和控制模式
- **配置驱动**: 通过Kconfig进行灵活配置
- **MCP兼容**: 标准MCP协议支持
- **硬件抽象**: 便于适配不同硬件配置

这种架构设计使得ESP32-MCP-HARDWARE成为一个可扩展的硬件控制平台，可以轻松添加新的硬件功能模块。
