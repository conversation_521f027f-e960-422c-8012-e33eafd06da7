menu "ESP32 MCP Hardware Configuration"

    menu "WiFi Configuration"
        config WIFI_SSID
            string "WiFi SSID"
            default "Your_WiFi_SSID"
            help
                SSID (network name) for the WiFi network to connect to.

        config WIFI_PASSWORD
            string "WiFi Password"
            default "Your_WiFi_Password"
            help
                WiFi password (WPA or WPA2) for the network.

        config WIFI_MAXIMUM_RETRY
            int "Maximum retry"
            default 5
            help
                Set the Maximum retry to avoid station reconnecting to the AP unlimited.
    endmenu

    menu "MCP Configuration"
        config MCP_ENDPOINT
            string "MCP WebSocket Endpoint"
            default "wss://api.xiaozhi.me/mcp/?token=your_token"
            help
                WebSocket endpoint URL for MCP server connection.

        config MCP_RECONNECT_INTERVAL
            int "MCP Reconnect Interval (ms)"
            default 5000
            help
                Interval between MCP reconnection attempts in milliseconds.

        config MCP_PING_INTERVAL
            int "MCP Ping Interval (ms)"
            default 30000
            help
                Interval between ping messages to keep connection alive.

        config MCP_BUFFER_SIZE
            int "MCP Message Buffer Size"
            default 2048
            help
                Size of the buffer for MCP messages.
    endmenu

    menu "Hardware Pin Configuration"
        config PIN_SCK
            int "SPI Clock Pin"
            default 19
            help
                GPIO pin number for SPI clock signal.

        config PIN_MOSI
            int "SPI MOSI Pin"
            default 20
            help
                GPIO pin number for SPI MOSI (Master Out Slave In) signal.

        config PIN_DC
            int "Display DC Pin"
            default 21
            help
                GPIO pin number for display Data/Command control.

        config PIN_RST
            int "Display Reset Pin"
            default 1
            help
                GPIO pin number for display reset (shared between both displays).

        config PIN_CS_LEFT
            int "Left Display CS Pin"
            default 2
            help
                GPIO pin number for left display chip select.

        config PIN_CS_RIGHT
            int "Right Display CS Pin"
            default 45
            help
                GPIO pin number for right display chip select.

        config LED_PIN
            int "Status LED Pin"
            default 2
            help
                GPIO pin number for status LED.
    endmenu

    menu "Eye Animation Configuration"
        config EYE_BLINK_INTERVAL_MIN
            int "Minimum Blink Interval (ms)"
            default 3000
            help
                Minimum interval between automatic eye blinks.

        config EYE_BLINK_INTERVAL_MAX
            int "Maximum Blink Interval (ms)"
            default 6000
            help
                Maximum interval between automatic eye blinks.

        config EYE_LOOK_INTERVAL_MIN
            int "Minimum Look Movement Interval (ms)"
            default 4000
            help
                Minimum interval between eye movement animations.

        config EYE_LOOK_INTERVAL_MAX
            int "Maximum Look Movement Interval (ms)"
            default 10000
            help
                Maximum interval between eye movement animations.

        config EYE_EMOTION_INTERVAL_MIN
            int "Minimum Emotion Change Interval (ms)"
            default 10000
            help
                Minimum interval between emotion changes.

        config EYE_EMOTION_INTERVAL_MAX
            int "Maximum Emotion Change Interval (ms)"
            default 20000
            help
                Maximum interval between emotion changes.

        config EYE_ANIMATION_TIMER_PERIOD
            int "Animation Timer Period (ms)"
            default 200
            help
                Period of the main animation timer in milliseconds.
    endmenu

endmenu
