/**
 * @file main.c
 * @brief 小智 ESP32 MCP 硬件系统主程序
 * 
 * 功能：
 * - 双屏模拟眼睛效果，动态，可随机变换动作，且动作切换平滑顺畅
 * - 提供MCP连接方式，支持使用小智AI进行MCP的连接与指令
 * - 可扩展性，能够接入多种硬件设备
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "esp_chip_info.h"
#include "esp_flash.h"

#include "common/app_config.h"
#include "common/wifi_manager.h"
#include "mcp_layer/mcp_client.h"
#include "eyes_layer/eye_display.h"

static const char *TAG = "MAIN";

// LED状态控制
static bool led_state = false;
static TaskHandle_t led_task_handle = NULL;

// 前向声明
static void led_task(void *pvParameters);
static void wifi_connection_callback(bool connected);
static void mcp_connection_callback(bool connected);
static void mcp_message_callback(const char* message);
static void mcp_error_callback(const char* error);
static void register_mcp_tools(void);

// MCP工具回调函数
static mcp_tool_response_t led_control_tool(const char* args);
static mcp_tool_response_t system_info_tool(const char* args);
static mcp_tool_response_t eye_control_tool(const char* args);
static mcp_tool_response_t calculator_tool(const char* args);

void app_main(void)
{
    ESP_LOGI(TAG, "🚀 小智 ESP32 MCP 硬件系统启动");
    ESP_LOGI(TAG, "📋 系统信息:");
    
    // 打印芯片信息
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    ESP_LOGI(TAG, "   - 芯片型号: %s", CONFIG_IDF_TARGET);
    ESP_LOGI(TAG, "   - CPU核心数: %d", chip_info.cores);
    ESP_LOGI(TAG, "   - WiFi%s%s", (chip_info.features & CHIP_FEATURE_WIFI_BGN) ? "/802.11bgn" : "",
             (chip_info.features & CHIP_FEATURE_BT) ? "/BT" : "");
    
    uint32_t flash_size;
    if (esp_flash_get_size(NULL, &flash_size) == ESP_OK) {
        ESP_LOGI(TAG, "   - Flash大小: %dMB", flash_size / (1024 * 1024));
    }
    
    ESP_LOGI(TAG, "   - 剩余内存: %d bytes", esp_get_free_heap_size());
    ESP_LOGI(TAG, "");

    // 1. 初始化LED
    ESP_LOGI(TAG, "💡 初始化状态LED");
    gpio_config_t led_config = {
        .pin_bit_mask = (1ULL << LED_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    ESP_ERROR_CHECK(gpio_config(&led_config));
    gpio_set_level(LED_PIN, 0);

    // 创建LED任务
    xTaskCreate(led_task, "led_task", 2048, NULL, 1, &led_task_handle);

    // 2. 初始化WiFi管理器
    ESP_LOGI(TAG, "📶 初始化WiFi管理器");
    ESP_ERROR_CHECK(wifi_manager_init());
    wifi_manager_set_callback(wifi_connection_callback);

    // 3. 初始化双屏眼睛显示系统
    ESP_LOGI(TAG, "👁️ 初始化双屏眼睛显示系统");
    ESP_ERROR_CHECK(eye_display_init());

    // 4. 启动WiFi连接
    ESP_LOGI(TAG, "🔗 连接WiFi: %s", CONFIG_WIFI_SSID);
    ESP_ERROR_CHECK(wifi_manager_start(CONFIG_WIFI_SSID, CONFIG_WIFI_PASSWORD));

    // 等待WiFi连接
    if (wifi_manager_wait_connected(30000)) {
        char ip_str[16];
        if (wifi_manager_get_ip(ip_str, sizeof(ip_str)) == ESP_OK) {
            ESP_LOGI(TAG, "✅ WiFi连接成功，IP地址: %s", ip_str);
            ESP_LOGI(TAG, "📶 信号强度: %d dBm", wifi_manager_get_rssi());
        }

        // 5. 初始化MCP客户端
        ESP_LOGI(TAG, "🔌 初始化MCP客户端");
        ESP_ERROR_CHECK(mcp_client_init(CONFIG_MCP_ENDPOINT));
        mcp_client_set_connection_callback(mcp_connection_callback);
        mcp_client_set_message_callback(mcp_message_callback);
        mcp_client_set_error_callback(mcp_error_callback);

        // 注册MCP工具
        register_mcp_tools();

        // 启动MCP客户端
        ESP_ERROR_CHECK(mcp_client_start());
    } else {
        ESP_LOGE(TAG, "❌ WiFi连接失败");
    }

    // 6. 启动双屏眼睛显示系统
    ESP_LOGI(TAG, "🎬 启动双屏眼睛显示系统");
    ESP_ERROR_CHECK(eye_display_start());

    ESP_LOGI(TAG, "🎉 系统初始化完成！");
    ESP_LOGI(TAG, "🌟 功能特性：");
    ESP_LOGI(TAG, "   ✨ 双屏模拟眼睛效果 - 动态表情，平滑动画");
    ESP_LOGI(TAG, "   🔗 MCP WebSocket连接 - 支持小智AI远程控制");
    ESP_LOGI(TAG, "   🛠️ 工具注册系统 - 可扩展硬件设备控制");
    ESP_LOGI(TAG, "   💡 LED状态指示 - 连接状态可视化");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "💡 LED状态指示说明：");
    ESP_LOGI(TAG, "   - 快速闪烁(100ms): WiFi未连接");
    ESP_LOGI(TAG, "   - 慢速闪烁(500ms): WiFi已连接，MCP未连接");
    ESP_LOGI(TAG, "   - 常亮: WiFi和MCP均已连接");

    // 主循环
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 定期打印系统状态
        static int status_counter = 0;
        if (++status_counter >= 60) { // 每60秒打印一次
            status_counter = 0;
            ESP_LOGI(TAG, "📊 系统状态 - WiFi: %s, MCP: %s, 剩余内存: %d bytes",
                     wifi_manager_is_connected() ? "已连接" : "未连接",
                     mcp_client_is_connected() ? "已连接" : "未连接",
                     esp_get_free_heap_size());
        }
    }
}

// LED任务 - 根据连接状态控制LED闪烁
static void led_task(void *pvParameters)
{
    while (1) {
        bool wifi_connected = wifi_manager_is_connected();
        bool mcp_connected = mcp_client_is_connected();

        if (!wifi_connected) {
            // WiFi未连接: 快速闪烁
            gpio_set_level(LED_PIN, 1);
            vTaskDelay(pdMS_TO_TICKS(100));
            gpio_set_level(LED_PIN, 0);
            vTaskDelay(pdMS_TO_TICKS(100));
        } else if (!mcp_connected) {
            // WiFi已连接但MCP未连接: 慢闪
            gpio_set_level(LED_PIN, 1);
            vTaskDelay(pdMS_TO_TICKS(500));
            gpio_set_level(LED_PIN, 0);
            vTaskDelay(pdMS_TO_TICKS(500));
        } else {
            // 全部连接成功: LED亮起
            gpio_set_level(LED_PIN, 1);
            vTaskDelay(pdMS_TO_TICKS(1000));
        }
    }
}

// WiFi连接状态回调
static void wifi_connection_callback(bool connected)
{
    if (connected) {
        ESP_LOGI(TAG, "📶 WiFi连接成功");
    } else {
        ESP_LOGW(TAG, "📶 WiFi连接断开");
    }
}

// MCP连接状态回调
static void mcp_connection_callback(bool connected)
{
    if (connected) {
        ESP_LOGI(TAG, "🔌 MCP服务器连接成功");
        ESP_LOGI(TAG, "🛠️ 已注册工具数量: %d", mcp_client_get_tool_count());
    } else {
        ESP_LOGW(TAG, "🔌 MCP服务器连接断开");
    }
}

// MCP消息接收回调
static void mcp_message_callback(const char* message)
{
    ESP_LOGD(TAG, "📨 MCP消息: %s", message);
}

// MCP错误回调
static void mcp_error_callback(const char* error)
{
    ESP_LOGE(TAG, "❌ MCP错误: %s", error);
}

// 注册MCP工具
static void register_mcp_tools(void)
{
    ESP_LOGI(TAG, "🛠️ 注册MCP工具...");

    // 1. LED控制工具
    mcp_client_register_tool(
        "led_control",
        "控制ESP32 LED状态",
        "{\"properties\":{\"state\":{\"title\":\"LED状态\",\"type\":\"string\",\"enum\":[\"on\",\"off\",\"blink\"]}},\"required\":[\"state\"],\"title\":\"ledControlArguments\",\"type\":\"object\"}",
        led_control_tool
    );

    // 2. 系统信息工具
    mcp_client_register_tool(
        "system_info",
        "获取ESP32系统信息",
        "{\"properties\":{},\"title\":\"systemInfoArguments\",\"type\":\"object\"}",
        system_info_tool
    );

    // 3. 眼睛控制工具
    mcp_client_register_tool(
        "eye_control",
        "控制双屏眼睛显示效果",
        "{\"properties\":{\"action\":{\"title\":\"动作\",\"type\":\"string\",\"enum\":[\"blink\",\"look\",\"emotion\"]},\"params\":{\"title\":\"参数\",\"type\":\"object\"}},\"required\":[\"action\"],\"title\":\"eyeControlArguments\",\"type\":\"object\"}",
        eye_control_tool
    );

    // 4. 计算器工具
    mcp_client_register_tool(
        "calculator",
        "简单计算器（支持加减乘除）",
        "{\"properties\":{\"expression\":{\"title\":\"表达式\",\"type\":\"string\"}},\"required\":[\"expression\"],\"title\":\"calculatorArguments\",\"type\":\"object\"}",
        calculator_tool
    );

    ESP_LOGI(TAG, "✅ MCP工具注册完成");
}

// LED控制工具实现
static mcp_tool_response_t led_control_tool(const char* args)
{
    ESP_LOGI(TAG, "🛠️ LED控制工具调用: %s", args);

    cJSON *json = cJSON_Parse(args);
    if (!json) {
        return mcp_create_response("{\"success\":false,\"error\":\"无效的参数格式\"}", true);
    }

    cJSON *state_item = cJSON_GetObjectItem(json, "state");
    if (!state_item || !cJSON_IsString(state_item)) {
        cJSON_Delete(json);
        return mcp_create_response("{\"success\":false,\"error\":\"缺少state参数\"}", true);
    }

    const char *state = state_item->valuestring;
    ESP_LOGI(TAG, "💡 LED控制: %s", state);

    if (strcmp(state, "on") == 0) {
        gpio_set_level(LED_PIN, 1);
        led_state = true;
    } else if (strcmp(state, "off") == 0) {
        gpio_set_level(LED_PIN, 0);
        led_state = false;
    } else if (strcmp(state, "blink") == 0) {
        // 触发闪烁效果
        for (int i = 0; i < 5; i++) {
            gpio_set_level(LED_PIN, 1);
            vTaskDelay(pdMS_TO_TICKS(200));
            gpio_set_level(LED_PIN, 0);
            vTaskDelay(pdMS_TO_TICKS(200));
        }
    }

    char response[256];
    snprintf(response, sizeof(response), "{\"success\":true,\"state\":\"%.32s\"}", state);

    cJSON_Delete(json);
    return mcp_create_response(response, false);
}

// 系统信息工具实现
static mcp_tool_response_t system_info_tool(const char* args)
{
    ESP_LOGI(TAG, "🛠️ 系统信息工具调用");

    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);

    uint32_t flash_size = 0;
    esp_flash_get_size(NULL, &flash_size);

    char ip_str[16] = "未连接";
    wifi_manager_get_ip(ip_str, sizeof(ip_str));

    char response[512];
    snprintf(response, sizeof(response),
        "{"
        "\"success\":true,"
        "\"model\":\"%s\","
        "\"cores\":%d,"
        "\"flashSize\":%d,"
        "\"freeHeap\":%d,"
        "\"wifiStatus\":\"%s\","
        "\"ipAddress\":\"%s\","
        "\"rssi\":%d,"
        "\"mcpStatus\":\"%s\","
        "\"eyeState\":%d"
        "}",
        CONFIG_IDF_TARGET,
        chip_info.cores,
        (int)(flash_size / 1024),
        (int)(esp_get_free_heap_size() / 1024),
        wifi_manager_is_connected() ? "connected" : "disconnected",
        ip_str,
        wifi_manager_get_rssi(),
        mcp_client_is_connected() ? "connected" : "disconnected",
        (int)eye_get_state()
    );

    return mcp_create_response(response, false);
}

// 眼睛控制工具实现
static mcp_tool_response_t eye_control_tool(const char* args)
{
    ESP_LOGI(TAG, "🛠️ 眼睛控制工具调用: %s", args);

    cJSON *json = cJSON_Parse(args);
    if (!json) {
        return mcp_create_response("{\"success\":false,\"error\":\"无效的参数格式\"}", true);
    }

    cJSON *action_item = cJSON_GetObjectItem(json, "action");
    if (!action_item || !cJSON_IsString(action_item)) {
        cJSON_Delete(json);
        return mcp_create_response("{\"success\":false,\"error\":\"缺少action参数\"}", true);
    }

    const char *action = action_item->valuestring;
    cJSON *params = cJSON_GetObjectItem(json, "params");

    char response[256];

    if (strcmp(action, "blink") == 0) {
        eye_trigger_blink();
        snprintf(response, sizeof(response), "{\"success\":true,\"action\":\"blink\",\"message\":\"眨眼动画已触发\"}");
    } else if (strcmp(action, "look") == 0) {
        int32_t x = 0, y = 0;
        if (params) {
            cJSON *x_item = cJSON_GetObjectItem(params, "x");
            cJSON *y_item = cJSON_GetObjectItem(params, "y");
            if (x_item && cJSON_IsNumber(x_item)) x = (int32_t)cJSON_GetNumberValue(x_item);
            if (y_item && cJSON_IsNumber(y_item)) y = (int32_t)cJSON_GetNumberValue(y_item);
        }
        eye_look_at(x, y);
        snprintf(response, sizeof(response), "{\"success\":true,\"action\":\"look\",\"x\":%d,\"y\":%d}", (int)x, (int)y);
    } else if (strcmp(action, "emotion") == 0) {
        eye_state_t emotion = EYE_STATE_NORMAL;
        if (params) {
            cJSON *emotion_item = cJSON_GetObjectItem(params, "emotion");
            if (emotion_item && cJSON_IsString(emotion_item)) {
                const char *emotion_str = emotion_item->valuestring;
                if (strcmp(emotion_str, "happy") == 0) emotion = EYE_STATE_HAPPY;
                else if (strcmp(emotion_str, "angry") == 0) emotion = EYE_STATE_ANGRY;
                else if (strcmp(emotion_str, "surprised") == 0) emotion = EYE_STATE_SURPRISED;
                else if (strcmp(emotion_str, "sleepy") == 0) emotion = EYE_STATE_SLEEPY;
            }
        }
        eye_set_emotion(emotion);
        snprintf(response, sizeof(response), "{\"success\":true,\"action\":\"emotion\",\"emotion\":%d}", (int)emotion);
    } else {
        snprintf(response, sizeof(response), "{\"success\":false,\"error\":\"未知的动作: %.32s\"}", action);
        cJSON_Delete(json);
        return mcp_create_response(response, true);
    }

    cJSON_Delete(json);
    return mcp_create_response(response, false);
}

// 计算器工具实现
static mcp_tool_response_t calculator_tool(const char* args)
{
    ESP_LOGI(TAG, "🛠️ 计算器工具调用: %s", args);

    cJSON *json = cJSON_Parse(args);
    if (!json) {
        return mcp_create_response("{\"success\":false,\"error\":\"无效的参数格式\"}", true);
    }

    cJSON *expr_item = cJSON_GetObjectItem(json, "expression");
    if (!expr_item || !cJSON_IsString(expr_item)) {
        cJSON_Delete(json);
        return mcp_create_response("{\"success\":false,\"error\":\"缺少expression参数\"}", true);
    }

    const char *expr = expr_item->valuestring;
    ESP_LOGI(TAG, "🧮 计算表达式: %s", expr);

    double result = 0;
    bool success = false;
    char error_msg[128] = "";

    // 简单的表达式解析（支持加减乘除）
    char *expr_copy = strdup(expr);
    if (expr_copy) {
        // 移除空格
        char *src = expr_copy, *dst = expr_copy;
        while (*src) {
            if (*src != ' ') *dst++ = *src;
            src++;
        }
        *dst = '\0';

        // 查找运算符
        char *op_pos = NULL;
        char op = 0;

        for (char *p = expr_copy + 1; *p; p++) { // 从第二个字符开始，支持负数
            if (*p == '+' || *p == '-' || *p == '*' || *p == '/') {
                op_pos = p;
                op = *p;
                break;
            }
        }

        if (op_pos) {
            *op_pos = '\0';
            double a = atof(expr_copy);
            double b = atof(op_pos + 1);

            switch (op) {
                case '+': result = a + b; success = true; break;
                case '-': result = a - b; success = true; break;
                case '*': result = a * b; success = true; break;
                case '/':
                    if (b != 0) {
                        result = a / b;
                        success = true;
                    } else {
                        strcpy(error_msg, "除数不能为零");
                    }
                    break;
            }
        } else {
            strcpy(error_msg, "无效的表达式格式");
        }

        free(expr_copy);
    } else {
        strcpy(error_msg, "内存分配失败");
    }

    char response[256];
    if (success) {
        snprintf(response, sizeof(response),
                "{\"success\":true,\"expression\":\"%.64s\",\"result\":%.6f}",
                expr, result);
    } else {
        snprintf(response, sizeof(response),
                "{\"success\":false,\"expression\":\"%.64s\",\"error\":\"%.64s\"}",
                expr, error_msg);
    }

    cJSON_Delete(json);
    return mcp_create_response(response, !success);
}
