/**
 * @file eye_display.h
 * @brief 双屏眼睛显示系统头文件
 * 
 * 基于LVGL 9.3.0的双屏模拟眼睛效果
 * 支持动态表情、平滑动画、情绪切换
 */

#ifndef EYE_DISPLAY_H
#define EYE_DISPLAY_H

#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_random.h"
#include "lvgl.h"
#include "app_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 眼睛状态枚举 ====================

typedef enum {
    EYE_STATE_NORMAL,      // 正常状态
    EYE_STATE_BLINKING,    // 眨眼状态
    EYE_STATE_LOOKING,     // 眼球移动状态
    EYE_STATE_HAPPY,       // 开心状态
    EYE_STATE_ANGRY,       // 生气状态
    EYE_STATE_SLEEPY,      // 困倦状态
    EYE_STATE_SURPRISED    // 惊讶状态
} eye_state_t;

// ==================== 眼睛结构体 ====================

typedef struct {
    lv_obj_t *eye_container;    // 眼球容器
    lv_obj_t *pupil;           // 瞳孔
    lv_obj_t *highlight;       // 高光点
    lv_obj_t *eyelid_top;      // 上眼睑
    lv_obj_t *eyelid_bottom;   // 下眼睑
    lv_anim_t blink_anim;      // 眨眼动画
    lv_anim_t look_anim;       // 眼球移动动画
    eye_state_t state;         // 当前状态
    int32_t pupil_x;           // 瞳孔X位置
    int32_t pupil_y;           // 瞳孔Y位置
    // 动画状态跟踪 - 解决抖动问题
    int32_t start_x;           // 动画起始X位置
    int32_t start_y;           // 动画起始Y位置
    int32_t target_x;          // 动画目标X位置
    int32_t target_y;          // 动画目标Y位置
    bool is_left_eye;          // 是否为左眼
} eye_t;

// ==================== 显示系统结构体 ====================

typedef struct {
    esp_lcd_panel_handle_t left_panel;
    esp_lcd_panel_handle_t right_panel;
    lv_disp_t *left_disp;
    lv_disp_t *right_disp;
    eye_t left_eye;
    eye_t right_eye;
    lv_timer_t *animation_timer;
    SemaphoreHandle_t lvgl_mutex;
    bool initialized;
} eye_display_system_t;

// ==================== 函数声明 ====================

/**
 * @brief 初始化双屏眼睛显示系统
 * @return esp_err_t 初始化结果
 */
esp_err_t eye_display_init(void);

/**
 * @brief 启动眼睛显示任务
 * @return esp_err_t 启动结果
 */
esp_err_t eye_display_start(void);

/**
 * @brief 停止眼睛显示系统
 */
void eye_display_stop(void);

/**
 * @brief 设置眼睛情绪
 * @param emotion 目标情绪
 */
void eye_set_emotion(eye_state_t emotion);

/**
 * @brief 触发眨眼动画
 */
void eye_trigger_blink(void);

/**
 * @brief 设置眼球看向指定位置
 * @param x X坐标偏移
 * @param y Y坐标偏移
 */
void eye_look_at(int32_t x, int32_t y);

/**
 * @brief 获取当前眼睛状态
 * @return eye_state_t 当前状态
 */
eye_state_t eye_get_state(void);

/**
 * @brief 获取显示系统句柄
 * @return eye_display_system_t* 系统句柄
 */
eye_display_system_t* eye_display_get_system(void);

/**
 * @brief LVGL互斥锁操作
 * @param timeout_ms 超时时间(ms)，-1表示永久等待
 * @return true 获取成功
 * @return false 获取失败
 */
bool eye_display_lock(int timeout_ms);

/**
 * @brief 释放LVGL互斥锁
 */
void eye_display_unlock(void);

#ifdef __cplusplus
}
#endif

#endif // EYE_DISPLAY_H
