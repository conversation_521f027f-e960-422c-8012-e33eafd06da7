/**
 * @file eye_display.c
 * @brief 双屏眼睛显示系统实现
 * 
 * 基于LVGL 9.3.0的双屏模拟眼睛效果
 * 移植自demo/main_lvgl_proper.c，适配ESP-IDF
 */

#include "eye_display.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "esp_lcd_gc9a01.h"

static const char *TAG = "EYE_DISPLAY";

// 全局显示系统实例
static eye_display_system_t g_display_system = {0};

// 前向声明
static esp_err_t init_gc9a01_panel(int cs_pin, esp_lcd_panel_handle_t *panel_handle, 
                                   lv_disp_drv_t *disp_drv, bool (*flush_ready_cb)(esp_lcd_panel_io_handle_t, esp_lcd_panel_io_event_data_t*, void*),
                                   void (*flush_cb)(lv_disp_drv_t*, const lv_area_t*, lv_color_t*));
static void create_eye_ui(lv_disp_t *disp, eye_t *eye, const char* eye_name);
static void eye_animation_timer_cb(lv_timer_t *timer);
static void lvgl_task(void *arg);
static void increase_lvgl_tick(void *arg);

// LVGL刷新完成回调 - 左屏
static bool notify_left_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
    lv_disp_flush_ready(disp_driver);
    return false;
}

// LVGL刷新完成回调 - 右屏
static bool notify_right_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
    lv_disp_flush_ready(disp_driver);
    return false;
}

// LVGL刷新回调 - 左屏
static void left_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)drv->user_data;
    esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_map);
}

// LVGL刷新回调 - 右屏
static void right_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)drv->user_data;
    esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_map);
}

// LVGL定时器回调
static void increase_lvgl_tick(void *arg)
{
    lv_tick_inc(LVGL_TICK_PERIOD_MS);
}

esp_err_t eye_display_init(void)
{
    if (g_display_system.initialized) {
        ESP_LOGW(TAG, "Display system already initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "🚀 初始化双屏眼睛显示系统");

    // 1. 初始化SPI总线
    ESP_LOGI(TAG, "🚌 初始化SPI总线");
    const spi_bus_config_t bus_cfg = GC9A01_PANEL_BUS_SPI_CONFIG(PIN_SCK, PIN_MOSI, SCREEN_WIDTH * SCREEN_HEIGHT);
    ESP_ERROR_CHECK(spi_bus_initialize(LCD_HOST, &bus_cfg, SPI_DMA_CH_AUTO));

    // 2. 初始化LVGL
    ESP_LOGI(TAG, "📚 初始化LVGL库");
    lv_init();

    // 3. 创建LVGL互斥锁
    g_display_system.lvgl_mutex = xSemaphoreCreateMutex();
    if (!g_display_system.lvgl_mutex) {
        ESP_LOGE(TAG, "Failed to create LVGL mutex");
        return ESP_ERR_NO_MEM;
    }

    // 4. 为左屏分配显示缓冲区
    ESP_LOGI(TAG, "💾 为左屏分配显示缓冲区");
    static lv_disp_draw_buf_t left_disp_buf;
    static lv_disp_drv_t left_disp_drv;
    lv_color_t *left_buf1 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    lv_color_t *left_buf2 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    if (!left_buf1 || !left_buf2) {
        ESP_LOGE(TAG, "Failed to allocate left display buffer");
        return ESP_ERR_NO_MEM;
    }
    lv_disp_draw_buf_init(&left_disp_buf, left_buf1, left_buf2, SCREEN_WIDTH * LVGL_BUFFER_HEIGHT);

    // 5. 初始化左屏
    ESP_ERROR_CHECK(init_gc9a01_panel(PIN_CS_LEFT, &g_display_system.left_panel, &left_disp_drv, 
                                      notify_left_flush_ready, left_flush_cb));
    left_disp_drv.draw_buf = &left_disp_buf;
    g_display_system.left_disp = lv_disp_drv_register(&left_disp_drv);

    // 6. 为右屏分配显示缓冲区
    ESP_LOGI(TAG, "💾 为右屏分配显示缓冲区");
    static lv_disp_draw_buf_t right_disp_buf;
    static lv_disp_drv_t right_disp_drv;
    lv_color_t *right_buf1 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    lv_color_t *right_buf2 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    if (!right_buf1 || !right_buf2) {
        ESP_LOGE(TAG, "Failed to allocate right display buffer");
        return ESP_ERR_NO_MEM;
    }
    lv_disp_draw_buf_init(&right_disp_buf, right_buf1, right_buf2, SCREEN_WIDTH * LVGL_BUFFER_HEIGHT);

    // 7. 初始化右屏
    ESP_ERROR_CHECK(init_gc9a01_panel(PIN_CS_RIGHT, &g_display_system.right_panel, &right_disp_drv, 
                                      notify_right_flush_ready, right_flush_cb));
    right_disp_drv.draw_buf = &right_disp_buf;
    g_display_system.right_disp = lv_disp_drv_register(&right_disp_drv);

    // 8. 创建LVGL定时器
    ESP_LOGI(TAG, "⏰ 创建LVGL定时器");
    const esp_timer_create_args_t lvgl_tick_timer_args = {
        .callback = &increase_lvgl_tick,
        .name = "lvgl_tick"
    };
    esp_timer_handle_t lvgl_tick_timer = NULL;
    ESP_ERROR_CHECK(esp_timer_create(&lvgl_tick_timer_args, &lvgl_tick_timer));
    ESP_ERROR_CHECK(esp_timer_start_periodic(lvgl_tick_timer, LVGL_TICK_PERIOD_MS * 1000));

    g_display_system.initialized = true;
    ESP_LOGI(TAG, "✅ 双屏眼睛显示系统初始化完成");
    return ESP_OK;
}

esp_err_t eye_display_start(void)
{
    if (!g_display_system.initialized) {
        ESP_LOGE(TAG, "Display system not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 创建LVGL任务
    ESP_LOGI(TAG, "🔒 创建LVGL任务");
    BaseType_t ret = xTaskCreate(lvgl_task, "LVGL", LVGL_TASK_STACK_SIZE, NULL, LVGL_TASK_PRIORITY, NULL);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create LVGL task");
        return ESP_ERR_NO_MEM;
    }

    ESP_LOGI(TAG, "🎉 双屏眼睛显示系统启动完成");
    return ESP_OK;
}

void eye_display_stop(void)
{
    if (g_display_system.animation_timer) {
        lv_timer_del(g_display_system.animation_timer);
        g_display_system.animation_timer = NULL;
    }
    
    g_display_system.initialized = false;
    ESP_LOGI(TAG, "双屏眼睛显示系统已停止");
}

bool eye_display_lock(int timeout_ms)
{
    if (!g_display_system.lvgl_mutex) {
        return false;
    }
    const TickType_t timeout_ticks = (timeout_ms == -1) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    return xSemaphoreTake(g_display_system.lvgl_mutex, timeout_ticks) == pdTRUE;
}

void eye_display_unlock(void)
{
    if (g_display_system.lvgl_mutex) {
        xSemaphoreGive(g_display_system.lvgl_mutex);
    }
}

eye_display_system_t* eye_display_get_system(void)
{
    return &g_display_system;
}

// 初始化单个GC9A01屏幕
static esp_err_t init_gc9a01_panel(int cs_pin, esp_lcd_panel_handle_t *panel_handle,
                                   lv_disp_drv_t *disp_drv, bool (*flush_ready_cb)(esp_lcd_panel_io_handle_t, esp_lcd_panel_io_event_data_t*, void*),
                                   void (*flush_cb)(lv_disp_drv_t*, const lv_area_t*, lv_color_t*))
{
    ESP_LOGI(TAG, "🔧 初始化GC9A01屏幕 (CS引脚: %d)", cs_pin);

    // 创建面板IO
    esp_lcd_panel_io_handle_t io_handle = NULL;
    const esp_lcd_panel_io_spi_config_t io_config = GC9A01_PANEL_IO_SPI_CONFIG(cs_pin, PIN_DC, flush_ready_cb, disp_drv);
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_HOST, &io_config, &io_handle));

    // 创建面板设备
    const esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = PIN_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,  // 使用RGB顺序
        .data_endian = LCD_RGB_DATA_ENDIAN_BIG,      // 大端序
        .bits_per_pixel = 16,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_gc9a01(io_handle, &panel_config, panel_handle));

    // 配置面板
    ESP_ERROR_CHECK(esp_lcd_panel_reset(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color(*panel_handle, true));  // 颜色反转
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(*panel_handle, true));

    // 初始化LVGL显示驱动
    lv_disp_drv_init(disp_drv);
    disp_drv->hor_res = SCREEN_WIDTH;
    disp_drv->ver_res = SCREEN_HEIGHT;
    disp_drv->flush_cb = flush_cb;
    disp_drv->user_data = *panel_handle;

    ESP_LOGI(TAG, "✅ GC9A01屏幕初始化完成 (CS引脚: %d)", cs_pin);
    return ESP_OK;
}

// 创建眼球UI
static void create_eye_ui(lv_disp_t *disp, eye_t *eye, const char* eye_name)
{
    ESP_LOGI(TAG, "👁️ 创建%s眼球UI", eye_name);

    // 设置当前显示器
    lv_disp_set_default(disp);

    // 获取当前屏幕 - 纯黑背景
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_set_style_bg_color(scr, lv_color_black(), 0);

    // 创建眼球容器 - 纯白眼球，无边框无阴影
    eye->eye_container = lv_obj_create(scr);
    lv_obj_set_size(eye->eye_container, 200, 200);
    lv_obj_center(eye->eye_container);
    lv_obj_set_style_radius(eye->eye_container, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->eye_container, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->eye_container, 0, 0);
    lv_obj_set_style_shadow_width(eye->eye_container, 0, 0);
    lv_obj_set_style_pad_all(eye->eye_container, 0, 0);

    // 创建瞳孔 - 纯黑，更大尺寸
    eye->pupil = lv_obj_create(eye->eye_container);
    lv_obj_set_size(eye->pupil, 100, 100);
    lv_obj_set_pos(eye->pupil, 50, 50);  // 居中位置 (200-100)/2 = 50
    lv_obj_set_style_radius(eye->pupil, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->pupil, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->pupil, 0, 0);
    lv_obj_set_style_pad_all(eye->pupil, 0, 0);

    // 创建大高光点 - 在瞳孔中心点的左下区域
    eye->highlight = lv_obj_create(eye->pupil);
    lv_obj_set_size(eye->highlight, 25, 25);
    lv_obj_set_pos(eye->highlight, 30, 55);
    lv_obj_set_style_radius(eye->highlight, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->highlight, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->highlight, 0, 0);
    lv_obj_set_style_pad_all(eye->highlight, 0, 0);

    // 小高光点 - 在右上区域
    lv_obj_t *highlight_small = lv_obj_create(eye->pupil);
    lv_obj_set_size(highlight_small, 12, 12);
    lv_obj_set_pos(highlight_small, 65, 20);
    lv_obj_set_style_radius(highlight_small, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(highlight_small, lv_color_white(), 0);
    lv_obj_set_style_border_width(highlight_small, 0, 0);
    lv_obj_set_style_pad_all(highlight_small, 0, 0);

    // 创建上眼睑 - 纯黑
    eye->eyelid_top = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_top, 200, 0);  // 初始高度为0
    lv_obj_align_to(eye->eyelid_top, eye->eye_container, LV_ALIGN_OUT_TOP_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_top, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->eyelid_top, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_top, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_top, 0, 0);

    // 创建下眼睑 - 纯黑
    eye->eyelid_bottom = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_bottom, 200, 0);  // 初始高度为0
    lv_obj_align_to(eye->eyelid_bottom, eye->eye_container, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_bottom, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_bottom, 0, 0);

    // 初始化眼睛状态
    eye->state = EYE_STATE_NORMAL;
    eye->pupil_x = 0;
    eye->pupil_y = 0;
    eye->start_x = 0;
    eye->start_y = 0;
    eye->target_x = 0;
    eye->target_y = 0;
    eye->is_left_eye = (eye == &g_display_system.left_eye);

    ESP_LOGI(TAG, "✅ %s眼球UI创建完成", eye_name);
}

// 动画回调函数
static void blink_ready_cb(lv_anim_t *a)
{
    eye_t *eye = (eye_t *)lv_anim_get_user_data(a);
    eye->state = EYE_STATE_NORMAL;
}

static void look_ready_cb(lv_anim_t *a)
{
    eye_t *eye = (eye_t *)lv_anim_get_user_data(a);
    eye->state = EYE_STATE_NORMAL;
}

// 上眼睑眨眼动画回调
static void blink_top_anim_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid_top = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid_top, value);
}

// 下眼睑眨眼动画回调
static void blink_bottom_anim_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid_bottom = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid_bottom, value);
}

// 统一的瞳孔移动动画回调
static void pupil_move_unified_cb(void *obj, int32_t value)
{
    eye_t *eye = (eye_t *)obj;

    // 计算当前动画进度 (0-1000)
    float progress = value / 1000.0f;

    // 使用插值计算当前位置
    int32_t current_x = eye->start_x + (eye->target_x - eye->start_x) * progress;
    int32_t current_y = eye->start_y + (eye->target_y - eye->start_y) * progress;

    // 计算瞳孔中心位置
    int32_t pupil_size = lv_obj_get_width(eye->pupil);
    int32_t center_x = (200 - pupil_size) / 2;
    int32_t center_y = (200 - pupil_size) / 2;

    // 设置瞳孔位置
    lv_obj_set_pos(eye->pupil, center_x + current_x, center_y + current_y);

    // 平滑更新高光位置
    int32_t highlight_x = 30 - current_x/4;
    int32_t highlight_y = 55 - current_y/4;

    // 限制高光在瞳孔范围内
    highlight_x = LV_CLAMP(10, highlight_x, pupil_size - 15);
    highlight_y = LV_CLAMP(15, highlight_y, pupil_size - 10);

    lv_obj_set_pos(eye->highlight, highlight_x, highlight_y);

    // 更新当前位置记录
    eye->pupil_x = current_x;
    eye->pupil_y = current_y;
}

// 创建眨眼动画
static void start_blink_animation(eye_t *eye)
{
    if (eye->state == EYE_STATE_BLINKING) return;

    // 暂停所有瞳孔移动动画，避免冲突
    lv_anim_del(eye, pupil_move_unified_cb);

    eye->state = EYE_STATE_BLINKING;

    // 上眼睑下降动画
    lv_anim_t anim_top;
    lv_anim_init(&anim_top);
    lv_anim_set_var(&anim_top, eye->eyelid_top);
    lv_anim_set_values(&anim_top, 0, 100);
    lv_anim_set_time(&anim_top, 120);
    lv_anim_set_exec_cb(&anim_top, blink_top_anim_cb);
    lv_anim_set_path_cb(&anim_top, lv_anim_path_ease_in);
    lv_anim_set_repeat_count(&anim_top, 1);
    lv_anim_set_playback_time(&anim_top, 180);
    lv_anim_set_ready_cb(&anim_top, blink_ready_cb);
    lv_anim_set_user_data(&anim_top, eye);
    lv_anim_start(&anim_top);

    // 下眼睑上升动画
    lv_anim_t anim_bottom;
    lv_anim_init(&anim_bottom);
    lv_anim_set_var(&anim_bottom, eye->eyelid_bottom);
    lv_anim_set_values(&anim_bottom, 0, 100);
    lv_anim_set_time(&anim_bottom, 120);
    lv_anim_set_exec_cb(&anim_bottom, blink_bottom_anim_cb);
    lv_anim_set_path_cb(&anim_bottom, lv_anim_path_ease_in);
    lv_anim_set_repeat_count(&anim_bottom, 1);
    lv_anim_set_playback_time(&anim_bottom, 180);
    lv_anim_start(&anim_bottom);
}

// 创建眼球移动动画
static void start_look_animation(eye_t *eye, int32_t target_x, int32_t target_y)
{
    if (eye->state == EYE_STATE_BLINKING) return;

    eye->state = EYE_STATE_LOOKING;

    // 限制移动范围
    target_x = LV_CLAMP(-15, target_x, 15);
    target_y = LV_CLAMP(-15, target_y, 15);

    // 停止之前的动画
    lv_anim_del(eye, pupil_move_unified_cb);

    // 设置动画起始和目标位置
    eye->start_x = eye->pupil_x;
    eye->start_y = eye->pupil_y;
    eye->target_x = target_x;
    eye->target_y = target_y;

    // 创建统一的移动动画
    lv_anim_t anim_unified;
    lv_anim_init(&anim_unified);
    lv_anim_set_var(&anim_unified, eye);
    lv_anim_set_values(&anim_unified, 0, 1000);
    lv_anim_set_time(&anim_unified, 800);
    lv_anim_set_exec_cb(&anim_unified, pupil_move_unified_cb);
    lv_anim_set_path_cb(&anim_unified, lv_anim_path_ease_in_out);
    lv_anim_set_ready_cb(&anim_unified, look_ready_cb);
    lv_anim_set_user_data(&anim_unified, eye);
    lv_anim_start(&anim_unified);

    // 更新瞳孔位置记录
    eye->pupil_x = target_x;
    eye->pupil_y = target_y;
}

// 情绪动画回调
static void emotion_pupil_size_cb(void *obj, int32_t value)
{
    lv_obj_t *pupil = (lv_obj_t *)obj;

    // 获取当前瞳孔位置
    lv_coord_t current_x = lv_obj_get_x(pupil);
    lv_coord_t current_y = lv_obj_get_y(pupil);

    // 计算当前相对于中心的偏移量
    int32_t old_size = lv_obj_get_width(pupil);
    int32_t old_center = (200 - old_size) / 2;
    int32_t offset_x = current_x - old_center;
    int32_t offset_y = current_y - old_center;

    // 设置新的瞳孔大小
    lv_obj_set_size(pupil, value, value);

    // 计算新的中心位置并保持偏移量
    int32_t new_center = (200 - value) / 2;
    lv_obj_set_pos(pupil, new_center + offset_x, new_center + offset_y);
}

static void emotion_eyelid_top_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid, value);
}

static void emotion_eyelid_bottom_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid, value);
}

// 设置眼睛情绪
static void set_eye_emotion(eye_t *eye, eye_state_t emotion)
{
    if (eye->state == emotion) return;

    // 获取当前瞳孔尺寸和眼睑高度
    int32_t current_pupil_size = lv_obj_get_width(eye->pupil);
    int32_t current_top_height = lv_obj_get_height(eye->eyelid_top);
    int32_t current_bottom_height = lv_obj_get_height(eye->eyelid_bottom);

    // 目标参数
    int32_t target_pupil_size = 100;
    int32_t target_top_height = 0;
    int32_t target_bottom_height = 0;

    switch (emotion) {
        case EYE_STATE_HAPPY:
            target_pupil_size = 110;
            target_bottom_height = 25;
            break;
        case EYE_STATE_ANGRY:
            target_pupil_size = 80;
            target_top_height = 35;
            break;
        case EYE_STATE_SURPRISED:
            target_pupil_size = 120;
            break;
        case EYE_STATE_SLEEPY:
            target_pupil_size = 90;
            target_top_height = 45;
            target_bottom_height = 45;
            break;
        default:
            break;
    }

    // 创建瞳孔尺寸动画
    if (current_pupil_size != target_pupil_size) {
        lv_anim_t pupil_anim;
        lv_anim_init(&pupil_anim);
        lv_anim_set_var(&pupil_anim, eye->pupil);
        lv_anim_set_values(&pupil_anim, current_pupil_size, target_pupil_size);
        lv_anim_set_time(&pupil_anim, 400);
        lv_anim_set_exec_cb(&pupil_anim, emotion_pupil_size_cb);
        lv_anim_set_path_cb(&pupil_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&pupil_anim);
    }

    // 创建上眼睑动画
    if (current_top_height != target_top_height) {
        lv_anim_t top_anim;
        lv_anim_init(&top_anim);
        lv_anim_set_var(&top_anim, eye->eyelid_top);
        lv_anim_set_values(&top_anim, current_top_height, target_top_height);
        lv_anim_set_time(&top_anim, 400);
        lv_anim_set_exec_cb(&top_anim, emotion_eyelid_top_cb);
        lv_anim_set_path_cb(&top_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&top_anim);
    }

    // 创建下眼睑动画
    if (current_bottom_height != target_bottom_height) {
        lv_anim_t bottom_anim;
        lv_anim_init(&bottom_anim);
        lv_anim_set_var(&bottom_anim, eye->eyelid_bottom);
        lv_anim_set_values(&bottom_anim, current_bottom_height, target_bottom_height);
        lv_anim_set_time(&bottom_anim, 400);
        lv_anim_set_exec_cb(&bottom_anim, emotion_eyelid_bottom_cb);
        lv_anim_set_path_cb(&bottom_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&bottom_anim);
    }

    eye->state = emotion;
}

// 眼睛动画定时器回调
static void eye_animation_timer_cb(lv_timer_t *timer)
{
    static uint32_t animation_counter = 0;
    static uint32_t last_blink_time = 0;
    static uint32_t last_look_time = 0;
    static uint32_t last_emotion_time = 0;

    uint32_t current_time = lv_tick_get();
    animation_counter++;

    // 随机眨眼 (每3-6秒)
    if (current_time - last_blink_time > (EYE_BLINK_INTERVAL_MIN + (esp_random() % (EYE_BLINK_INTERVAL_MAX - EYE_BLINK_INTERVAL_MIN)))) {
        if (g_display_system.left_eye.state == EYE_STATE_NORMAL && g_display_system.right_eye.state == EYE_STATE_NORMAL) {
            start_blink_animation(&g_display_system.left_eye);
            start_blink_animation(&g_display_system.right_eye);
            last_blink_time = current_time;
            ESP_LOGD(TAG, "👁️ 执行眨眼动画");
        }
    }

    // 随机眼球移动 (每4-10秒)
    if (current_time - last_look_time > (EYE_LOOK_INTERVAL_MIN + (esp_random() % (EYE_LOOK_INTERVAL_MAX - EYE_LOOK_INTERVAL_MIN)))) {
        if (g_display_system.left_eye.state == EYE_STATE_NORMAL && g_display_system.right_eye.state == EYE_STATE_NORMAL) {
            // 生成随机目标位置
            int32_t target_x = (esp_random() % 24) - 12;  // -12 到 +12
            int32_t target_y = (esp_random() % 24) - 12;  // -12 到 +12

            start_look_animation(&g_display_system.left_eye, target_x, target_y);
            start_look_animation(&g_display_system.right_eye, target_x, target_y);
            last_look_time = current_time;
            ESP_LOGD(TAG, "👁️ 平滑移动到 (%d, %d)", (int)target_x, (int)target_y);
        }
    }

    // 随机情绪变化 (每10-20秒)
    if (current_time - last_emotion_time > (EYE_EMOTION_INTERVAL_MIN + (esp_random() % (EYE_EMOTION_INTERVAL_MAX - EYE_EMOTION_INTERVAL_MIN)))) {
        if (g_display_system.left_eye.state == EYE_STATE_NORMAL && g_display_system.right_eye.state == EYE_STATE_NORMAL) {
            eye_state_t emotions[] = {EYE_STATE_HAPPY, EYE_STATE_SURPRISED, EYE_STATE_SLEEPY};
            eye_state_t emotion = emotions[esp_random() % 3];

            set_eye_emotion(&g_display_system.left_eye, emotion);
            set_eye_emotion(&g_display_system.right_eye, emotion);
            last_emotion_time = current_time;

            ESP_LOGD(TAG, "😊 情绪切换: %d", (int)emotion);

            // 2秒后恢复正常
            lv_timer_t *restore_timer = lv_timer_create([](lv_timer_t *t) {
                set_eye_emotion(&g_display_system.left_eye, EYE_STATE_NORMAL);
                set_eye_emotion(&g_display_system.right_eye, EYE_STATE_NORMAL);
                lv_timer_del(t);
            }, 2000, NULL);
            lv_timer_set_repeat_count(restore_timer, 1);
        }
    }
}

// LVGL任务
static void lvgl_task(void *arg)
{
    ESP_LOGI(TAG, "🚀 启动LVGL任务");

    // 等待显示器初始化完成
    vTaskDelay(pdMS_TO_TICKS(100));

    // 创建双眼UI
    create_eye_ui(g_display_system.left_disp, &g_display_system.left_eye, "左");
    create_eye_ui(g_display_system.right_disp, &g_display_system.right_eye, "右");

    // 创建眼睛动画定时器
    g_display_system.animation_timer = lv_timer_create(eye_animation_timer_cb, EYE_ANIMATION_TIMER_PERIOD, NULL);

    ESP_LOGI(TAG, "🎉 双眼UI创建完成，开始LVGL主循环");
    ESP_LOGI(TAG, "👁️ 眼睛动画系统已启动");

    uint32_t task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
    while (1) {
        // 获取互斥锁并处理LVGL
        if (eye_display_lock(-1)) {
            task_delay_ms = lv_timer_handler();
            eye_display_unlock();
        }

        // 限制任务延迟范围
        if (task_delay_ms > LVGL_TASK_MAX_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
        } else if (task_delay_ms < LVGL_TASK_MIN_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MIN_DELAY_MS;
        }

        vTaskDelay(pdMS_TO_TICKS(task_delay_ms));
    }
}

// 公共API实现
void eye_set_emotion(eye_state_t emotion)
{
    if (eye_display_lock(100)) {
        set_eye_emotion(&g_display_system.left_eye, emotion);
        set_eye_emotion(&g_display_system.right_eye, emotion);
        eye_display_unlock();
    }
}

void eye_trigger_blink(void)
{
    if (eye_display_lock(100)) {
        start_blink_animation(&g_display_system.left_eye);
        start_blink_animation(&g_display_system.right_eye);
        eye_display_unlock();
    }
}

void eye_look_at(int32_t x, int32_t y)
{
    if (eye_display_lock(100)) {
        start_look_animation(&g_display_system.left_eye, x, y);
        start_look_animation(&g_display_system.right_eye, x, y);
        eye_display_unlock();
    }
}

eye_state_t eye_get_state(void)
{
    return g_display_system.left_eye.state;
}
