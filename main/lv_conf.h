/**
 * @file lv_conf.h
 * LVGL配置文件 - ESP32-MCP-HARDWARE项目
 * 基于LVGL 9.3.0，优化用于双GC9A01显示屏
 */

#ifndef LV_CONF_H
#define LV_CONF_H

#include "sdkconfig.h"

/*====================
   基础配置
 *====================*/

/* 启用LVGL */
#define LV_CONF_INCLUDE_SIMPLE      1

/* 颜色深度 */
#define LV_COLOR_DEPTH              16

/* 字节序配置 - 关键配置 */
#define LV_COLOR_16_SWAP            1    /* 启用字节交换 */

/* 内存配置 */
#define LV_MEM_CUSTOM               0
#define LV_MEM_SIZE                 (64U * 1024U)  /* 64KB内存池 */

/* 显示刷新配置 */
#define LV_DISP_DEF_REFR_PERIOD     30
#define LV_INDEV_DEF_READ_PERIOD    30

/* DPI配置 */
#define LV_DPI_DEF                  130

/*====================
   基础组件启用
 *====================*/

/* 基础组件 */
#define LV_USE_ARC                  1
#define LV_USE_IMG                  1
#define LV_USE_LABEL                1

/*====================
   字体配置
 *====================*/

/* 内置字体 */
#define LV_FONT_MONTSERRAT_14       1

/* 默认字体 */
#define LV_FONT_DEFAULT             &lv_font_montserrat_14

/*====================
   动画配置
 *====================*/

/* 动画 */
#define LV_USE_ANIMATION            1
#if LV_USE_ANIMATION
    #define LV_ANIM_DEFAULT_TIME    400
    #define LV_ANIM_DEFAULT_DELAY   0
    #define LV_ANIM_DEFAULT_REPEAT  1
#endif

/*====================
   主题配置
 *====================*/

/* 主题 */
#define LV_USE_THEME_DEFAULT        1
#if LV_USE_THEME_DEFAULT
    #define LV_THEME_DEFAULT_DARK   0
    #define LV_THEME_DEFAULT_GROW   1
    #define LV_THEME_DEFAULT_TRANSITION_TIME 80
#endif

/*====================
   调试配置
 *====================*/

/* 日志配置 */
#define LV_USE_LOG                  1
#if LV_USE_LOG
    #define LV_LOG_LEVEL            LV_LOG_LEVEL_WARN
    #define LV_LOG_PRINTF           1
#endif

/* 断言 */
#define LV_USE_ASSERT_NULL          1
#define LV_USE_ASSERT_MALLOC        1

/* 用户数据 */
#define LV_USE_USER_DATA            1

/*====================
   编译器配置
 *====================*/

/* 对于ESP32 */
#define LV_ATTRIBUTE_TICK_INC       IRAM_ATTR
#define LV_ATTRIBUTE_TIMER_HANDLER  IRAM_ATTR
#define LV_ATTRIBUTE_FLUSH_READY    IRAM_ATTR

/* 大端序配置 */
#define LV_BIG_ENDIAN_SYSTEM        0

/* 内存对齐 */
#define LV_MEM_BUF_MAX_NUM          16

/* 字符串配置 */
#define LV_SPRINTF_CUSTOM           0
#define LV_SPRINTF_USE_FLOAT        0

#endif /*LV_CONF_H*/
