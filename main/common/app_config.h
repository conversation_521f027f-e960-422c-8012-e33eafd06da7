/**
 * @file app_config.h
 * @brief 应用程序配置头文件
 * 
 * 小智 ESP32 MCP 硬件系统配置
 * 功能：双屏模拟眼睛效果 + MCP连接
 */

#ifndef APP_CONFIG_H
#define APP_CONFIG_H

#include "sdkconfig.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 硬件配置 ====================

// SPI配置
#define LCD_HOST                SPI2_HOST
#define PIN_SCK                 19    // SPI时钟
#define PIN_MOSI                20    // SPI数据输出
#define PIN_DC                  21    // 数据/命令控制
#define PIN_RST                 1     // 复位引脚（共享）
#define PIN_CS_LEFT             2     // 左屏片选
#define PIN_CS_RIGHT            45    // 右屏片选

// 屏幕配置
#define SCREEN_WIDTH            240
#define SCREEN_HEIGHT           240

// LED状态指示
#define LED_PIN                 2     // ESP32板载LED

// ==================== LVGL配置 ====================

#define LVGL_TICK_PERIOD_MS     2
#define LVGL_TASK_MAX_DELAY_MS  500
#define LVGL_TASK_MIN_DELAY_MS  1
#define LVGL_TASK_STACK_SIZE    (8 * 1024)
#define LVGL_TASK_PRIORITY      2
#define LVGL_BUFFER_HEIGHT      60    // 缓冲区高度（行数）

// ==================== WiFi配置 ====================

#ifndef CONFIG_WIFI_SSID
#define CONFIG_WIFI_SSID        "Xiaomi"
#endif

#ifndef CONFIG_WIFI_PASSWORD
#define CONFIG_WIFI_PASSWORD    "88888888"
#endif

#define WIFI_MAXIMUM_RETRY      5
#define WIFI_CONNECTED_BIT      BIT0
#define WIFI_FAIL_BIT           BIT1

// ==================== MCP配置 ====================

#ifndef CONFIG_MCP_ENDPOINT
#define CONFIG_MCP_ENDPOINT     "wss://api.xiaozhi.me/mcp/?token=wss://api.xiaozhi.me/mcp/?token=***********************************************************************************************************************************************************************************************************************************************************************"
#endif

#define MCP_RECONNECT_INTERVAL  5000  // 重连间隔(ms)
#define MCP_PING_INTERVAL       30000 // ping间隔(ms)
#define MCP_BUFFER_SIZE         2048  // 消息缓冲区大小

// ==================== 调试配置 ====================

#define DEBUG_SERIAL_BAUD       115200

// ==================== 眼睛动画配置 ====================

#define EYE_BLINK_INTERVAL_MIN  3000  // 最小眨眼间隔(ms)
#define EYE_BLINK_INTERVAL_MAX  6000  // 最大眨眼间隔(ms)
#define EYE_LOOK_INTERVAL_MIN   4000  // 最小眼球移动间隔(ms)
#define EYE_LOOK_INTERVAL_MAX   10000 // 最大眼球移动间隔(ms)
#define EYE_EMOTION_INTERVAL_MIN 10000 // 最小情绪变化间隔(ms)
#define EYE_EMOTION_INTERVAL_MAX 20000 // 最大情绪变化间隔(ms)

#define EYE_ANIMATION_TIMER_PERIOD 200 // 动画定时器周期(ms)

#ifdef __cplusplus
}
#endif

#endif // APP_CONFIG_H
