/**
 * @file mcp_protocol.h
 * @brief MCP协议实现接口
 * 
 * 将原有的MCP客户端重构为通信协议插件
 */

#ifndef MCP_PROTOCOL_H
#define MCP_PROTOCOL_H

#include "comm_manager.h"
#include "esp_websocket_client.h"
#include "cJSON.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== MCP协议常量定义 ====================

#define MCP_PROTOCOL_NAME           "mcp"
#define MCP_PROTOCOL_VERSION        0x010000  // v1.0.0
#define MCP_DEFAULT_PORT            8080
#define MCP_BUFFER_SIZE             4096
#define MCP_PING_INTERVAL           30000     // 30秒
#define MCP_RECONNECT_INTERVAL      5000      // 5秒

// ==================== MCP消息类型定义 ====================

/**
 * @brief MCP消息类型枚举
 */
typedef enum {
    MCP_MSG_TYPE_INITIALIZE = 0,
    MCP_MSG_TYPE_TOOLS_LIST,
    MCP_MSG_TYPE_TOOLS_CALL,
    MCP_MSG_TYPE_NOTIFICATION,
    MCP_MSG_TYPE_PING,
    MCP_MSG_TYPE_PONG
} mcp_message_type_t;

/**
 * @brief MCP工具定义
 */
typedef struct {
    char name[64];                  // 工具名称
    char description[256];          // 工具描述
    cJSON* input_schema;            // 输入参数模式
    void* handler;                  // 工具处理函数
} mcp_tool_t;

/**
 * @brief MCP协议配置
 */
typedef struct {
    char server_url[256];           // 服务器URL
    uint16_t port;                  // 端口号
    char client_name[64];           // 客户端名称
    char client_version[32];        // 客户端版本
    uint32_t buffer_size;           // 缓冲区大小
    uint32_t ping_interval_ms;      // Ping间隔
    uint32_t reconnect_interval_ms; // 重连间隔
    bool enable_compression;        // 启用压缩
    bool enable_heartbeat;          // 启用心跳
} mcp_protocol_config_t;

/**
 * @brief MCP协议上下文
 */
typedef struct {
    mcp_protocol_config_t config;
    esp_websocket_client_handle_t ws_client;
    comm_state_t state;
    uint32_t request_id;
    mcp_tool_t* tools;
    uint32_t tool_count;
    uint32_t max_tools;
    comm_connection_stats_t stats;
    
    // 回调函数
    comm_state_callback_t state_callback;
    comm_message_callback_t message_callback;
    comm_error_callback_t error_callback;
    
    // 同步对象
    void* mutex;
    void* event_group;
} mcp_protocol_context_t;

// ==================== MCP协议接口 ====================

/**
 * @brief 获取MCP协议信息
 * @return 协议信息指针
 */
const comm_protocol_info_t* mcp_protocol_get_info(void);

/**
 * @brief 注册MCP协议
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_register(void);

/**
 * @brief 注销MCP协议
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_unregister(void);

// ==================== MCP工具管理接口 ====================

/**
 * @brief 注册MCP工具
 * @param context 协议上下文
 * @param tool 工具定义
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_register_tool(mcp_protocol_context_t* context, const mcp_tool_t* tool);

/**
 * @brief 注销MCP工具
 * @param context 协议上下文
 * @param tool_name 工具名称
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_unregister_tool(mcp_protocol_context_t* context, const char* tool_name);

/**
 * @brief 获取工具列表
 * @param context 协议上下文
 * @param tools 工具列表输出
 * @param max_count 最大数量
 * @return 实际工具数量
 */
uint32_t mcp_protocol_get_tools(mcp_protocol_context_t* context, mcp_tool_t* tools, uint32_t max_count);

/**
 * @brief 调用工具
 * @param context 协议上下文
 * @param tool_name 工具名称
 * @param params 参数
 * @param result 结果输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_call_tool(mcp_protocol_context_t* context, const char* tool_name, const cJSON* params, cJSON** result);

// ==================== MCP消息处理接口 ====================

/**
 * @brief 发送初始化消息
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_initialize(mcp_protocol_context_t* context);

/**
 * @brief 发送工具列表
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_tools_list(mcp_protocol_context_t* context);

/**
 * @brief 发送工具调用响应
 * @param context 协议上下文
 * @param request_id 请求ID
 * @param result 结果
 * @param error 错误信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_tool_response(mcp_protocol_context_t* context, uint32_t request_id, const cJSON* result, const char* error);

/**
 * @brief 发送通知
 * @param context 协议上下文
 * @param method 方法名
 * @param params 参数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_notification(mcp_protocol_context_t* context, const char* method, const cJSON* params);

/**
 * @brief 发送Ping
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_ping(mcp_protocol_context_t* context);

/**
 * @brief 发送Pong
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send_pong(mcp_protocol_context_t* context);

// ==================== MCP协议操作实现 ====================

/**
 * @brief 初始化MCP协议
 * @param config 连接配置
 * @param context 上下文输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_init(const comm_connection_config_t* config, void** context);

/**
 * @brief 反初始化MCP协议
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_deinit(void* context);

/**
 * @brief 连接MCP服务器
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_connect(void* context);

/**
 * @brief 断开MCP连接
 * @param context 协议上下文
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_disconnect(void* context);

/**
 * @brief 发送MCP消息
 * @param context 协议上下文
 * @param message 消息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_send(void* context, const comm_message_t* message);

/**
 * @brief 接收MCP消息
 * @param context 协议上下文
 * @param message 消息输出
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_receive(void* context, comm_message_t* message, uint32_t timeout_ms);

/**
 * @brief 获取MCP连接状态
 * @param context 协议上下文
 * @return 连接状态
 */
comm_state_t mcp_protocol_get_state(void* context);

/**
 * @brief 获取MCP统计信息
 * @param context 协议上下文
 * @param stats 统计信息输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t mcp_protocol_get_stats(void* context, comm_connection_stats_t* stats);

// ==================== 便捷宏定义 ====================

/**
 * @brief 默认MCP协议配置
 */
#define MCP_PROTOCOL_DEFAULT_CONFIG() { \
    .server_url = "ws://localhost:8080/mcp", \
    .port = MCP_DEFAULT_PORT, \
    .client_name = "ESP32-MCP-Client", \
    .client_version = "1.0.0", \
    .buffer_size = MCP_BUFFER_SIZE, \
    .ping_interval_ms = MCP_PING_INTERVAL, \
    .reconnect_interval_ms = MCP_RECONNECT_INTERVAL, \
    .enable_compression = false, \
    .enable_heartbeat = true \
}

/**
 * @brief 创建MCP连接配置
 */
#define MCP_CREATE_CONNECTION_CONFIG(url) { \
    .protocol = COMM_PROTOCOL_MCP, \
    .endpoint = url, \
    .port = MCP_DEFAULT_PORT, \
    .username = "", \
    .password = "", \
    .auto_reconnect = true, \
    .reconnect_interval_ms = MCP_RECONNECT_INTERVAL, \
    .heartbeat_interval_ms = MCP_PING_INTERVAL, \
    .timeout_ms = 30000, \
    .protocol_config = NULL \
}

#ifdef __cplusplus
}
#endif

#endif // MCP_PROTOCOL_H
