/**
 * @file comm_manager.h
 * @brief 通信管理器接口定义
 * 
 * 通信管理器负责统一管理所有通信协议和连接，提供统一的通信接口
 */

#ifndef COMM_MANAGER_H
#define COMM_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 通信协议类型定义 ====================

/**
 * @brief 通信协议类型枚举
 */
typedef enum {
    COMM_PROTOCOL_UNKNOWN = 0,
    COMM_PROTOCOL_MCP,          // MCP协议
    COMM_PROTOCOL_MQTT,         // MQTT协议
    COMM_PROTOCOL_HTTP,         // HTTP协议
    COMM_PROTOCOL_WEBSOCKET,    // WebSocket协议
    COMM_PROTOCOL_TCP,          // TCP协议
    COMM_PROTOCOL_UDP,          // UDP协议
    COMM_PROTOCOL_SERIAL,       // 串口协议
    COMM_PROTOCOL_CUSTOM        // 自定义协议
} comm_protocol_type_t;

/**
 * @brief 连接状态枚举
 */
typedef enum {
    COMM_STATE_DISCONNECTED = 0,
    COMM_STATE_CONNECTING,
    COMM_STATE_CONNECTED,
    COMM_STATE_RECONNECTING,
    COMM_STATE_ERROR
} comm_state_t;

/**
 * @brief 消息类型枚举
 */
typedef enum {
    COMM_MSG_TYPE_DATA = 0,     // 数据消息
    COMM_MSG_TYPE_COMMAND,      // 命令消息
    COMM_MSG_TYPE_RESPONSE,     // 响应消息
    COMM_MSG_TYPE_EVENT,        // 事件消息
    COMM_MSG_TYPE_HEARTBEAT     // 心跳消息
} comm_message_type_t;

// ==================== 通信连接定义 ====================

/**
 * @brief 连接ID类型
 */
typedef uint32_t comm_connection_id_t;

/**
 * @brief 通信消息结构
 */
typedef struct {
    uint32_t id;                    // 消息ID
    comm_message_type_t type;       // 消息类型
    comm_connection_id_t conn_id;   // 连接ID
    void* data;                     // 消息数据
    size_t data_size;               // 数据大小
    uint32_t timestamp;             // 时间戳
    bool need_response;             // 是否需要响应
    uint32_t timeout_ms;            // 超时时间
} comm_message_t;

/**
 * @brief 连接配置
 */
typedef struct {
    comm_protocol_type_t protocol;  // 协议类型
    char endpoint[128];             // 连接端点
    uint16_t port;                  // 端口号
    char username[64];              // 用户名
    char password[64];              // 密码
    bool auto_reconnect;            // 自动重连
    uint32_t reconnect_interval_ms; // 重连间隔
    uint32_t heartbeat_interval_ms; // 心跳间隔
    uint32_t timeout_ms;            // 超时时间
    void* protocol_config;          // 协议特定配置
} comm_connection_config_t;

/**
 * @brief 连接统计信息
 */
typedef struct {
    uint32_t messages_sent;         // 发送消息数
    uint32_t messages_received;     // 接收消息数
    uint32_t bytes_sent;            // 发送字节数
    uint32_t bytes_received;        // 接收字节数
    uint32_t reconnect_count;       // 重连次数
    uint32_t error_count;           // 错误次数
    uint32_t uptime_seconds;        // 连接时间
} comm_connection_stats_t;

// ==================== 回调函数类型 ====================

/**
 * @brief 连接状态变化回调
 */
typedef void (*comm_state_callback_t)(comm_connection_id_t conn_id, comm_state_t old_state, comm_state_t new_state);

/**
 * @brief 消息接收回调
 */
typedef void (*comm_message_callback_t)(comm_connection_id_t conn_id, const comm_message_t* message);

/**
 * @brief 错误回调
 */
typedef void (*comm_error_callback_t)(comm_connection_id_t conn_id, esp_err_t error_code, const char* error_msg);

// ==================== 协议接口定义 ====================

/**
 * @brief 协议操作接口
 */
typedef struct {
    esp_err_t (*init)(const comm_connection_config_t* config, void** context);
    esp_err_t (*deinit)(void* context);
    esp_err_t (*connect)(void* context);
    esp_err_t (*disconnect)(void* context);
    esp_err_t (*send)(void* context, const comm_message_t* message);
    esp_err_t (*receive)(void* context, comm_message_t* message, uint32_t timeout_ms);
    comm_state_t (*get_state)(void* context);
    esp_err_t (*get_stats)(void* context, comm_connection_stats_t* stats);
} comm_protocol_ops_t;

/**
 * @brief 协议注册信息
 */
typedef struct {
    const char* name;               // 协议名称
    comm_protocol_type_t type;      // 协议类型
    uint32_t version;               // 版本号
    const comm_protocol_ops_t* ops; // 操作接口
} comm_protocol_info_t;

// ==================== 通信管理器配置 ====================

/**
 * @brief 通信管理器配置
 */
typedef struct {
    uint32_t max_connections;       // 最大连接数
    uint32_t message_queue_size;    // 消息队列大小
    uint32_t worker_stack_size;     // 工作任务堆栈大小
    uint32_t worker_priority;       // 工作任务优先级
    bool enable_stats;              // 启用统计
    bool enable_logging;            // 启用日志
} comm_manager_config_t;

/**
 * @brief 默认通信管理器配置
 */
#define COMM_MANAGER_DEFAULT_CONFIG() { \
    .max_connections = 8, \
    .message_queue_size = 32, \
    .worker_stack_size = 4096, \
    .worker_priority = 5, \
    .enable_stats = true, \
    .enable_logging = true \
}

// ==================== 通信管理器接口 ====================

/**
 * @brief 初始化通信管理器
 * @param config 管理器配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_init(const comm_manager_config_t* config);

/**
 * @brief 反初始化通信管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_deinit(void);

/**
 * @brief 启动通信管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_start(void);

/**
 * @brief 停止通信管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_stop(void);

// ==================== 协议管理接口 ====================

/**
 * @brief 注册通信协议
 * @param protocol_info 协议信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_register_protocol(const comm_protocol_info_t* protocol_info);

/**
 * @brief 注销通信协议
 * @param protocol_name 协议名称
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_unregister_protocol(const char* protocol_name);

// ==================== 连接管理接口 ====================

/**
 * @brief 创建连接
 * @param config 连接配置
 * @return 连接ID，0表示失败
 */
comm_connection_id_t comm_manager_create_connection(const comm_connection_config_t* config);

/**
 * @brief 销毁连接
 * @param conn_id 连接ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_destroy_connection(comm_connection_id_t conn_id);

/**
 * @brief 连接
 * @param conn_id 连接ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_connect(comm_connection_id_t conn_id);

/**
 * @brief 断开连接
 * @param conn_id 连接ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_disconnect(comm_connection_id_t conn_id);

/**
 * @brief 获取连接状态
 * @param conn_id 连接ID
 * @return 连接状态
 */
comm_state_t comm_manager_get_connection_state(comm_connection_id_t conn_id);

// ==================== 消息传递接口 ====================

/**
 * @brief 发送消息
 * @param conn_id 连接ID
 * @param message 消息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_send_message(comm_connection_id_t conn_id, const comm_message_t* message);

/**
 * @brief 广播消息
 * @param message 消息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_broadcast_message(const comm_message_t* message);

/**
 * @brief 创建消息
 * @param type 消息类型
 * @param data 数据
 * @param data_size 数据大小
 * @return 消息指针，NULL表示失败
 */
comm_message_t* comm_manager_create_message(comm_message_type_t type, const void* data, size_t data_size);

/**
 * @brief 销毁消息
 * @param message 消息指针
 */
void comm_manager_destroy_message(comm_message_t* message);

// ==================== 回调管理接口 ====================

/**
 * @brief 设置状态变化回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_set_state_callback(comm_state_callback_t callback);

/**
 * @brief 设置消息接收回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_set_message_callback(comm_message_callback_t callback);

/**
 * @brief 设置错误回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_set_error_callback(comm_error_callback_t callback);

// ==================== 统计信息接口 ====================

/**
 * @brief 获取连接统计信息
 * @param conn_id 连接ID
 * @param stats 统计信息输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_get_connection_stats(comm_connection_id_t conn_id, comm_connection_stats_t* stats);

/**
 * @brief 重置连接统计信息
 * @param conn_id 连接ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t comm_manager_reset_connection_stats(comm_connection_id_t conn_id);

#ifdef __cplusplus
}
#endif

#endif // COMM_MANAGER_H
