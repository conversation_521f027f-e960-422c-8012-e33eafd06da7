/**
 * @file hardware_manager.c
 * @brief 硬件管理器实现
 * 
 * 硬件管理器负责统一管理所有硬件设备
 */

#include "hardware_manager.h"
#include "hal_interface.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>
#include <stdlib.h>

static const char* TAG = "hardware_manager";

// ==================== 内部数据结构 ====================

/**
 * @brief 硬件管理器内部状态
 */
typedef struct {
    bool initialized;
    bool running;
    hardware_manager_config_t config;
    hal_device_t* device_list;          // 设备链表
    uint32_t device_count;              // 设备数量
    SemaphoreHandle_t mutex;            // 互斥锁
    TaskHandle_t scan_task;             // 扫描任务句柄
    hardware_event_callback_t event_callbacks[8]; // 事件回调列表
    uint32_t callback_count;            // 回调数量
    hardware_manager_stats_t stats;     // 统计信息
} hardware_manager_t;

static hardware_manager_t g_hw_manager = {0};

// ==================== 内部函数声明 ====================

static void hardware_scan_task(void* pvParameters);
static esp_err_t send_device_event(hardware_event_type_t type, hal_device_t* device, esp_err_t error_code);
static hal_device_t* find_device_in_list(hardware_id_t device_id);
static esp_err_t add_device_to_list(hal_device_t* device);
static esp_err_t remove_device_from_list(hardware_id_t device_id);

// ==================== 硬件管理器接口实现 ====================

esp_err_t hardware_manager_init(const hardware_manager_config_t* config)
{
    if (g_hw_manager.initialized) {
        ESP_LOGW(TAG, "Hardware manager already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return ESP_ERR_INVALID_ARG;
    }

    // 初始化管理器状态
    memset(&g_hw_manager, 0, sizeof(hardware_manager_t));
    g_hw_manager.config = *config;
    
    // 创建互斥锁
    g_hw_manager.mutex = xSemaphoreCreateMutex();
    if (!g_hw_manager.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // 初始化HAL系统
    esp_err_t ret = hal_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize HAL: %s", esp_err_to_name(ret));
        vSemaphoreDelete(g_hw_manager.mutex);
        return ret;
    }

    g_hw_manager.initialized = true;
    ESP_LOGI(TAG, "Hardware manager initialized successfully");
    
    return ESP_OK;
}

esp_err_t hardware_manager_deinit(void)
{
    if (!g_hw_manager.initialized) {
        return ESP_OK;
    }

    // 停止管理器
    hardware_manager_stop();

    // 清理所有设备
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        hal_device_t* current = g_hw_manager.device_list;
        while (current) {
            hal_device_t* next = current->next;
            hal_destroy_device(current);
            current = next;
        }
        g_hw_manager.device_list = NULL;
        g_hw_manager.device_count = 0;
        xSemaphoreGive(g_hw_manager.mutex);
    }

    // 反初始化HAL系统
    hal_deinit();

    // 删除互斥锁
    if (g_hw_manager.mutex) {
        vSemaphoreDelete(g_hw_manager.mutex);
        g_hw_manager.mutex = NULL;
    }

    g_hw_manager.initialized = false;
    ESP_LOGI(TAG, "Hardware manager deinitialized");
    
    return ESP_OK;
}

esp_err_t hardware_manager_start(void)
{
    if (!g_hw_manager.initialized) {
        ESP_LOGE(TAG, "Hardware manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_hw_manager.running) {
        ESP_LOGW(TAG, "Hardware manager already running");
        return ESP_OK;
    }

    // 创建设备扫描任务
    BaseType_t ret = xTaskCreate(
        hardware_scan_task,
        "hw_scan",
        4096,
        NULL,
        5,
        &g_hw_manager.scan_task
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create scan task");
        return ESP_ERR_NO_MEM;
    }

    g_hw_manager.running = true;
    g_hw_manager.stats.uptime_seconds = 0;
    
    ESP_LOGI(TAG, "Hardware manager started");
    return ESP_OK;
}

esp_err_t hardware_manager_stop(void)
{
    if (!g_hw_manager.running) {
        return ESP_OK;
    }

    g_hw_manager.running = false;

    // 删除扫描任务
    if (g_hw_manager.scan_task) {
        vTaskDelete(g_hw_manager.scan_task);
        g_hw_manager.scan_task = NULL;
    }

    ESP_LOGI(TAG, "Hardware manager stopped");
    return ESP_OK;
}

// ==================== 设备管理接口实现 ====================

esp_err_t hardware_manager_add_device(hal_device_t* device)
{
    if (!device) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!g_hw_manager.initialized) {
        ESP_LOGE(TAG, "Hardware manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = add_device_to_list(device);
    if (ret == ESP_OK) {
        // 发送设备添加事件
        send_device_event(HW_EVENT_DEVICE_ADDED, device, ESP_OK);
        
        // 自动初始化设备
        if (g_hw_manager.config.auto_init_devices) {
            ret = hal_device_init(device);
            if (ret == ESP_OK) {
                send_device_event(HW_EVENT_DEVICE_INITIALIZED, device, ESP_OK);
            } else {
                send_device_event(HW_EVENT_DEVICE_ERROR, device, ret);
            }
        }
    }

    return ret;
}

esp_err_t hardware_manager_remove_device(hardware_id_t device_id)
{
    hal_device_t* device = find_device_in_list(device_id);
    if (!device) {
        return ESP_ERR_NOT_FOUND;
    }

    // 反初始化设备
    hal_device_deinit(device);
    
    // 发送设备移除事件
    send_device_event(HW_EVENT_DEVICE_REMOVED, device, ESP_OK);
    
    // 从列表中移除
    esp_err_t ret = remove_device_from_list(device_id);
    if (ret == ESP_OK) {
        hal_destroy_device(device);
    }

    return ret;
}

hal_device_t* hardware_manager_find_device(hardware_id_t device_id)
{
    return find_device_in_list(device_id);
}

uint32_t hardware_manager_get_devices(hardware_type_t type, hal_device_t** devices, uint32_t max_count)
{
    if (!devices || max_count == 0) {
        return 0;
    }

    uint32_t count = 0;
    
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        hal_device_t* current = g_hw_manager.device_list;
        while (current && count < max_count) {
            if (type == HW_TYPE_UNKNOWN || current->base.type == type) {
                devices[count++] = current;
            }
            current = current->next;
        }
        xSemaphoreGive(g_hw_manager.mutex);
    }

    return count;
}

uint32_t hardware_manager_get_device_count(hardware_type_t type)
{
    uint32_t count = 0;
    
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        if (type == HW_TYPE_UNKNOWN) {
            count = g_hw_manager.device_count;
        } else {
            hal_device_t* current = g_hw_manager.device_list;
            while (current) {
                if (current->base.type == type) {
                    count++;
                }
                current = current->next;
            }
        }
        xSemaphoreGive(g_hw_manager.mutex);
    }

    return count;
}

// ==================== 设备操作接口实现 ====================

esp_err_t hardware_manager_init_device(hardware_id_t device_id)
{
    hal_device_t* device = find_device_in_list(device_id);
    if (!device) {
        return ESP_ERR_NOT_FOUND;
    }

    esp_err_t ret = hal_device_init(device);
    if (ret == ESP_OK) {
        send_device_event(HW_EVENT_DEVICE_INITIALIZED, device, ESP_OK);
    } else {
        send_device_event(HW_EVENT_DEVICE_ERROR, device, ret);
    }

    return ret;
}

esp_err_t hardware_manager_deinit_device(hardware_id_t device_id)
{
    hal_device_t* device = find_device_in_list(device_id);
    if (!device) {
        return ESP_ERR_NOT_FOUND;
    }

    return hal_device_deinit(device);
}

hardware_state_t hardware_manager_get_device_state(hardware_id_t device_id)
{
    hal_device_t* device = find_device_in_list(device_id);
    if (!device) {
        return HW_STATE_UNINITIALIZED;
    }

    return hal_device_get_state(device);
}

// ==================== 事件管理接口实现 ====================

esp_err_t hardware_manager_register_event_callback(hardware_event_callback_t callback, void* user_data)
{
    if (!callback) {
        return ESP_ERR_INVALID_ARG;
    }

    if (g_hw_manager.callback_count >= sizeof(g_hw_manager.event_callbacks) / sizeof(g_hw_manager.event_callbacks[0])) {
        ESP_LOGE(TAG, "Too many event callbacks registered");
        return ESP_ERR_NO_MEM;
    }

    g_hw_manager.event_callbacks[g_hw_manager.callback_count++] = callback;
    return ESP_OK;
}

esp_err_t hardware_manager_send_event(const hardware_event_data_t* event_data)
{
    if (!event_data) {
        return ESP_ERR_INVALID_ARG;
    }

    // 调用所有注册的回调函数
    for (uint32_t i = 0; i < g_hw_manager.callback_count; i++) {
        if (g_hw_manager.event_callbacks[i]) {
            g_hw_manager.event_callbacks[i](event_data);
        }
    }

    g_hw_manager.stats.events_sent++;
    return ESP_OK;
}

// ==================== 统计信息接口实现 ====================

esp_err_t hardware_manager_get_stats(hardware_manager_stats_t* stats)
{
    if (!stats) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        *stats = g_hw_manager.stats;
        stats->total_devices = g_hw_manager.device_count;
        
        // 计算活跃设备数和错误设备数
        stats->active_devices = 0;
        stats->error_devices = 0;
        
        hal_device_t* current = g_hw_manager.device_list;
        while (current) {
            if (current->base.state == HW_STATE_ACTIVE) {
                stats->active_devices++;
            } else if (current->base.state == HW_STATE_ERROR) {
                stats->error_devices++;
            }
            current = current->next;
        }
        
        xSemaphoreGive(g_hw_manager.mutex);
    }

    return ESP_OK;
}

// ==================== 内部函数实现 ====================

static void hardware_scan_task(void* pvParameters)
{
    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (g_hw_manager.running) {
        // 更新运行时间
        g_hw_manager.stats.uptime_seconds++;
        
        // 扫描设备状态
        if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
            hal_device_t* current = g_hw_manager.device_list;
            while (current) {
                hardware_state_t old_state = current->base.state;
                hardware_state_t new_state = hal_device_get_state(current);
                
                if (old_state != new_state) {
                    current->base.state = new_state;
                    
                    hardware_event_data_t event = {
                        .type = HW_EVENT_DEVICE_STATE_CHANGED,
                        .device_id = current->base.id,
                        .device_type = current->base.type,
                        .old_state = old_state,
                        .new_state = new_state,
                        .error_code = ESP_OK,
                        .user_data = NULL
                    };
                    
                    hardware_manager_send_event(&event);
                }
                
                current = current->next;
            }
            xSemaphoreGive(g_hw_manager.mutex);
        }
        
        // 等待下次扫描
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(g_hw_manager.config.scan_interval_ms));
    }
    
    vTaskDelete(NULL);
}

static esp_err_t send_device_event(hardware_event_type_t type, hal_device_t* device, esp_err_t error_code)
{
    hardware_event_data_t event = {
        .type = type,
        .device_id = device->base.id,
        .device_type = device->base.type,
        .old_state = device->base.state,
        .new_state = device->base.state,
        .error_code = error_code,
        .user_data = NULL
    };
    
    return hardware_manager_send_event(&event);
}

static hal_device_t* find_device_in_list(hardware_id_t device_id)
{
    hal_device_t* result = NULL;
    
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        hal_device_t* current = g_hw_manager.device_list;
        while (current) {
            if (current->base.id == device_id) {
                result = current;
                break;
            }
            current = current->next;
        }
        xSemaphoreGive(g_hw_manager.mutex);
    }
    
    return result;
}

static esp_err_t add_device_to_list(hal_device_t* device)
{
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 检查设备是否已存在
        hal_device_t* current = g_hw_manager.device_list;
        while (current) {
            if (current->base.id == device->base.id) {
                xSemaphoreGive(g_hw_manager.mutex);
                return ESP_ERR_INVALID_STATE;
            }
            current = current->next;
        }
        
        // 添加到链表头部
        device->next = g_hw_manager.device_list;
        g_hw_manager.device_list = device;
        g_hw_manager.device_count++;
        
        xSemaphoreGive(g_hw_manager.mutex);
        return ESP_OK;
    }
    
    return ESP_ERR_TIMEOUT;
}

static esp_err_t remove_device_from_list(hardware_id_t device_id)
{
    esp_err_t result = ESP_ERR_NOT_FOUND;
    
    if (xSemaphoreTake(g_hw_manager.mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        hal_device_t** current = &g_hw_manager.device_list;
        
        while (*current) {
            if ((*current)->base.id == device_id) {
                hal_device_t* to_remove = *current;
                *current = to_remove->next;
                g_hw_manager.device_count--;
                result = ESP_OK;
                break;
            }
            current = &((*current)->next);
        }
        
        xSemaphoreGive(g_hw_manager.mutex);
    }
    
    return result;
}
