# 硬件抽象层(HAL) CMakeLists.txt

# 收集所有源文件
set(HARDWARE_SRCS
    # 核心HAL源文件
    "src/hardware_manager.c"
    "src/hal_interface.c"
    
    # 显示设备源文件
    "display/src/display_manager.c"
    "display/src/gc9a01_driver.c"
    "display/src/lvgl_adapter.c"
    "display/src/eye_display.c"
    
    # 传感器设备源文件
    "sensors/src/sensor_manager.c"
    "sensors/src/temperature_sensor.c"
    "sensors/src/light_sensor.c"
    "sensors/src/motion_sensor.c"
    
    # 执行器设备源文件
    "actuators/src/actuator_manager.c"
    "actuators/src/led_controller.c"
    "actuators/src/servo_controller.c"
    "actuators/src/buzzer_controller.c"
    
    # 连接设备源文件
    "connectivity/src/connectivity_manager.c"
    "connectivity/src/wifi_manager.c"
    "connectivity/src/bluetooth_manager.c"
    "connectivity/src/ethernet_manager.c"
)

# 收集所有头文件目录
set(HARDWARE_INCLUDES
    # 核心HAL头文件
    "include"
    
    # 显示设备头文件
    "display/include"
    
    # 传感器设备头文件
    "sensors/include"
    
    # 执行器设备头文件
    "actuators/include"
    
    # 连接设备头文件
    "connectivity/include"
)

# 定义依赖的ESP-IDF组件
set(HARDWARE_REQUIRES
    # 基础组件
    esp_common
    esp_hw_support
    esp_system
    freertos
    log
    driver
    esp_timer
    nvs_flash
    
    # 显示相关组件
    esp_lcd
    lvgl
    spi_flash
    
    # 网络相关组件
    esp_wifi
    esp_netif
    esp_event
    esp_eth
    
    # 蓝牙相关组件
    bt
    
    # JSON解析组件
    json
)

# 注册硬件抽象层组件
idf_component_register(
    SRCS ${HARDWARE_SRCS}
    INCLUDE_DIRS ${HARDWARE_INCLUDES}
    REQUIRES ${HARDWARE_REQUIRES}
    PRIV_REQUIRES esp_lcd_panel_vendor
)

# 设置编译选项
target_compile_options(${COMPONENT_LIB} PRIVATE
    -Wno-format
    -Wno-unused-function
    -Wno-unused-variable
    -Wno-missing-field-initializers
)

# 添加预处理器定义
target_compile_definitions(${COMPONENT_LIB} PRIVATE
    HAL_VERSION_MAJOR=1
    HAL_VERSION_MINOR=0
    HAL_VERSION_PATCH=0
)

# 条件编译支持
if(CONFIG_HARDWARE_DISPLAY_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE HARDWARE_DISPLAY_ENABLED=1)
endif()

if(CONFIG_HARDWARE_SENSORS_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE HARDWARE_SENSORS_ENABLED=1)
endif()

if(CONFIG_HARDWARE_ACTUATORS_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE HARDWARE_ACTUATORS_ENABLED=1)
endif()

if(CONFIG_HARDWARE_CONNECTIVITY_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE HARDWARE_CONNECTIVITY_ENABLED=1)
endif()

# 显示设备特定配置
if(CONFIG_DISPLAY_GC9A01_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE DISPLAY_GC9A01_ENABLED=1)
endif()

if(CONFIG_DISPLAY_LVGL_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE DISPLAY_LVGL_ENABLED=1)
endif()

# 传感器特定配置
if(CONFIG_SENSOR_TEMPERATURE_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE SENSOR_TEMPERATURE_ENABLED=1)
endif()

if(CONFIG_SENSOR_LIGHT_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE SENSOR_LIGHT_ENABLED=1)
endif()

if(CONFIG_SENSOR_MOTION_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE SENSOR_MOTION_ENABLED=1)
endif()

# 执行器特定配置
if(CONFIG_ACTUATOR_LED_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE ACTUATOR_LED_ENABLED=1)
endif()

if(CONFIG_ACTUATOR_SERVO_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE ACTUATOR_SERVO_ENABLED=1)
endif()

if(CONFIG_ACTUATOR_BUZZER_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE ACTUATOR_BUZZER_ENABLED=1)
endif()

# 连接设备特定配置
if(CONFIG_CONNECTIVITY_WIFI_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE CONNECTIVITY_WIFI_ENABLED=1)
endif()

if(CONFIG_CONNECTIVITY_BLUETOOTH_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE CONNECTIVITY_BLUETOOTH_ENABLED=1)
endif()

if(CONFIG_CONNECTIVITY_ETHERNET_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE CONNECTIVITY_ETHERNET_ENABLED=1)
endif()

# 调试配置
if(CONFIG_HARDWARE_DEBUG_ENABLE)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE 
        HARDWARE_DEBUG_ENABLED=1
        LOG_LOCAL_LEVEL=ESP_LOG_DEBUG
    )
endif()

# 性能优化配置
if(CONFIG_HARDWARE_OPTIMIZE_PERFORMANCE)
    target_compile_options(${COMPONENT_LIB} PRIVATE -O2)
else()
    target_compile_options(${COMPONENT_LIB} PRIVATE -Os)
endif()

# 内存优化配置
if(CONFIG_HARDWARE_OPTIMIZE_MEMORY)
    target_compile_definitions(${COMPONENT_LIB} PRIVATE HARDWARE_MEMORY_OPTIMIZED=1)
endif()

# 添加版本信息
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/include/hardware_version.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/include/hardware_version.h"
    @ONLY
)

target_include_directories(${COMPONENT_LIB} PRIVATE
    "${CMAKE_CURRENT_BINARY_DIR}/include"
)

# 生成硬件配置头文件
set(HARDWARE_CONFIG_FILE "${CMAKE_CURRENT_BINARY_DIR}/include/hardware_config_generated.h")
file(WRITE ${HARDWARE_CONFIG_FILE} "/* Auto-generated hardware configuration */\n")
file(APPEND ${HARDWARE_CONFIG_FILE} "#ifndef HARDWARE_CONFIG_GENERATED_H\n")
file(APPEND ${HARDWARE_CONFIG_FILE} "#define HARDWARE_CONFIG_GENERATED_H\n\n")

# 添加硬件模块配置
if(CONFIG_HARDWARE_DISPLAY_ENABLE)
    file(APPEND ${HARDWARE_CONFIG_FILE} "#define HARDWARE_DISPLAY_ENABLED 1\n")
endif()

if(CONFIG_HARDWARE_SENSORS_ENABLE)
    file(APPEND ${HARDWARE_CONFIG_FILE} "#define HARDWARE_SENSORS_ENABLED 1\n")
endif()

if(CONFIG_HARDWARE_ACTUATORS_ENABLE)
    file(APPEND ${HARDWARE_CONFIG_FILE} "#define HARDWARE_ACTUATORS_ENABLED 1\n")
endif()

if(CONFIG_HARDWARE_CONNECTIVITY_ENABLE)
    file(APPEND ${HARDWARE_CONFIG_FILE} "#define HARDWARE_CONNECTIVITY_ENABLED 1\n")
endif()

file(APPEND ${HARDWARE_CONFIG_FILE} "\n#endif // HARDWARE_CONFIG_GENERATED_H\n")

# 添加构建信息
message(STATUS "Hardware Abstraction Layer Configuration:")
message(STATUS "  Display Support: ${CONFIG_HARDWARE_DISPLAY_ENABLE}")
message(STATUS "  Sensors Support: ${CONFIG_HARDWARE_SENSORS_ENABLE}")
message(STATUS "  Actuators Support: ${CONFIG_HARDWARE_ACTUATORS_ENABLE}")
message(STATUS "  Connectivity Support: ${CONFIG_HARDWARE_CONNECTIVITY_ENABLE}")
message(STATUS "  Debug Mode: ${CONFIG_HARDWARE_DEBUG_ENABLE}")
message(STATUS "  Performance Optimization: ${CONFIG_HARDWARE_OPTIMIZE_PERFORMANCE}")
message(STATUS "  Memory Optimization: ${CONFIG_HARDWARE_OPTIMIZE_MEMORY}")

# 添加自定义目标用于代码检查
add_custom_target(hardware_lint
    COMMAND cppcheck --enable=all --std=c99 --platform=unix32 
            --suppress=missingIncludeSystem 
            --suppress=unusedFunction
            --suppress=unmatchedSuppression
            ${CMAKE_CURRENT_SOURCE_DIR}/src/
            ${CMAKE_CURRENT_SOURCE_DIR}/display/src/
            ${CMAKE_CURRENT_SOURCE_DIR}/sensors/src/
            ${CMAKE_CURRENT_SOURCE_DIR}/actuators/src/
            ${CMAKE_CURRENT_SOURCE_DIR}/connectivity/src/
    COMMENT "Running static analysis on hardware layer"
    VERBATIM
)

# 添加自定义目标用于生成文档
add_custom_target(hardware_docs
    COMMAND doxygen ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Generating hardware layer documentation"
    VERBATIM
)

# 添加测试支持
if(CONFIG_HARDWARE_ENABLE_TESTS)
    add_subdirectory(tests)
endif()
