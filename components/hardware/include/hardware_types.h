/**
 * @file hardware_types.h
 * @brief 硬件类型定义和通用数据结构
 * 
 * 定义了硬件抽象层中使用的所有数据类型、枚举和结构体
 */

#ifndef HARDWARE_TYPES_H
#define HARDWARE_TYPES_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 通用硬件类型 ====================

/**
 * @brief 硬件设备类型枚举
 */
typedef enum {
    HW_TYPE_UNKNOWN = 0,
    HW_TYPE_DISPLAY,        // 显示设备
    HW_TYPE_SENSOR,         // 传感器
    HW_TYPE_ACTUATOR,       // 执行器
    HW_TYPE_CONNECTIVITY,   // 连接设备
    HW_TYPE_MAX
} hardware_type_t;

/**
 * @brief 硬件设备状态枚举
 */
typedef enum {
    HW_STATE_UNINITIALIZED = 0,
    HW_STATE_INITIALIZING,
    HW_STATE_READY,
    HW_STATE_ACTIVE,
    HW_STATE_ERROR,
    HW_STATE_SUSPENDED
} hardware_state_t;

/**
 * @brief 硬件设备ID类型
 */
typedef uint32_t hardware_id_t;

/**
 * @brief 硬件设备句柄类型
 */
typedef void* hardware_handle_t;

// ==================== 显示设备类型 ====================

/**
 * @brief 显示设备子类型
 */
typedef enum {
    DISPLAY_TYPE_UNKNOWN = 0,
    DISPLAY_TYPE_LCD,
    DISPLAY_TYPE_OLED,
    DISPLAY_TYPE_EINK,
    DISPLAY_TYPE_LED_MATRIX
} display_type_t;

/**
 * @brief 显示设备配置
 */
typedef struct {
    display_type_t type;
    uint16_t width;
    uint16_t height;
    uint8_t color_depth;
    bool double_buffer;
    int8_t cs_pin;
    int8_t dc_pin;
    int8_t rst_pin;
    int8_t backlight_pin;
} display_config_t;

// ==================== 传感器类型 ====================

/**
 * @brief 传感器类型枚举
 */
typedef enum {
    SENSOR_TYPE_UNKNOWN = 0,
    SENSOR_TYPE_TEMPERATURE,
    SENSOR_TYPE_HUMIDITY,
    SENSOR_TYPE_PRESSURE,
    SENSOR_TYPE_LIGHT,
    SENSOR_TYPE_MOTION,
    SENSOR_TYPE_PROXIMITY,
    SENSOR_TYPE_ACCELEROMETER,
    SENSOR_TYPE_GYROSCOPE,
    SENSOR_TYPE_MAGNETOMETER,
    SENSOR_TYPE_GPS
} sensor_type_t;

/**
 * @brief 传感器数据类型
 */
typedef union {
    float temperature;      // 温度 (°C)
    float humidity;         // 湿度 (%)
    float pressure;         // 压力 (Pa)
    float light;           // 光照强度 (lux)
    bool motion;           // 运动检测
    float distance;        // 距离 (cm)
    struct {               // 三轴数据
        float x, y, z;
    } axis3d;
    struct {               // GPS数据
        double latitude;
        double longitude;
        float altitude;
    } gps;
} sensor_data_t;

/**
 * @brief 传感器配置
 */
typedef struct {
    sensor_type_t type;
    uint32_t sample_rate;   // 采样率 (Hz)
    uint8_t precision;      // 精度位数
    bool auto_calibrate;    // 自动校准
    int8_t power_pin;       // 电源控制引脚
    int8_t data_pin;        // 数据引脚
    int8_t clock_pin;       // 时钟引脚
} sensor_config_t;

// ==================== 执行器类型 ====================

/**
 * @brief 执行器类型枚举
 */
typedef enum {
    ACTUATOR_TYPE_UNKNOWN = 0,
    ACTUATOR_TYPE_LED,
    ACTUATOR_TYPE_SERVO,
    ACTUATOR_TYPE_MOTOR,
    ACTUATOR_TYPE_BUZZER,
    ACTUATOR_TYPE_RELAY,
    ACTUATOR_TYPE_VALVE
} actuator_type_t;

/**
 * @brief LED控制数据
 */
typedef struct {
    uint8_t red;
    uint8_t green;
    uint8_t blue;
    uint8_t brightness;
    bool blink;
    uint32_t blink_period;
} led_data_t;

/**
 * @brief 舵机控制数据
 */
typedef struct {
    float angle;            // 角度 (0-180度)
    uint32_t speed;         // 转动速度
} servo_data_t;

/**
 * @brief 执行器控制数据
 */
typedef union {
    led_data_t led;
    servo_data_t servo;
    float motor_speed;      // 电机速度 (-100 到 100)
    struct {                // 蜂鸣器
        uint32_t frequency;
        uint32_t duration;
    } buzzer;
    bool relay_state;       // 继电器状态
} actuator_data_t;

/**
 * @brief 执行器配置
 */
typedef struct {
    actuator_type_t type;
    int8_t control_pin;
    int8_t power_pin;
    uint32_t max_current;   // 最大电流 (mA)
    bool invert_logic;      // 反向逻辑
} actuator_config_t;

// ==================== 连接设备类型 ====================

/**
 * @brief 连接类型枚举
 */
typedef enum {
    CONN_TYPE_UNKNOWN = 0,
    CONN_TYPE_WIFI,
    CONN_TYPE_BLUETOOTH,
    CONN_TYPE_ETHERNET,
    CONN_TYPE_CELLULAR,
    CONN_TYPE_LORA
} connectivity_type_t;

/**
 * @brief 连接状态枚举
 */
typedef enum {
    CONN_STATE_DISCONNECTED = 0,
    CONN_STATE_CONNECTING,
    CONN_STATE_CONNECTED,
    CONN_STATE_ERROR
} connectivity_state_t;

/**
 * @brief WiFi配置
 */
typedef struct {
    char ssid[32];
    char password[64];
    bool auto_reconnect;
    uint8_t max_retry;
} wifi_config_t;

/**
 * @brief 连接配置
 */
typedef struct {
    connectivity_type_t type;
    union {
        wifi_config_t wifi;
        // 其他连接类型配置可在此添加
    } config;
} connectivity_config_t;

// ==================== 通用硬件设备结构 ====================

/**
 * @brief 硬件设备描述符
 */
typedef struct {
    hardware_id_t id;
    hardware_type_t type;
    hardware_state_t state;
    char name[32];
    char description[64];
    void* config;           // 指向具体配置结构
    void* private_data;     // 私有数据
} hardware_device_t;

/**
 * @brief 硬件操作回调函数类型
 */
typedef esp_err_t (*hw_init_func_t)(hardware_device_t* device);
typedef esp_err_t (*hw_deinit_func_t)(hardware_device_t* device);
typedef esp_err_t (*hw_read_func_t)(hardware_device_t* device, void* data, size_t size);
typedef esp_err_t (*hw_write_func_t)(hardware_device_t* device, const void* data, size_t size);
typedef esp_err_t (*hw_control_func_t)(hardware_device_t* device, uint32_t cmd, void* arg);

/**
 * @brief 硬件操作接口
 */
typedef struct {
    hw_init_func_t init;
    hw_deinit_func_t deinit;
    hw_read_func_t read;
    hw_write_func_t write;
    hw_control_func_t control;
} hardware_ops_t;

// ==================== 错误代码 ====================

#define ESP_ERR_HW_BASE                 0x8000
#define ESP_ERR_HW_NOT_FOUND           (ESP_ERR_HW_BASE + 1)
#define ESP_ERR_HW_NOT_INITIALIZED     (ESP_ERR_HW_BASE + 2)
#define ESP_ERR_HW_ALREADY_INITIALIZED (ESP_ERR_HW_BASE + 3)
#define ESP_ERR_HW_INVALID_CONFIG      (ESP_ERR_HW_BASE + 4)
#define ESP_ERR_HW_OPERATION_FAILED    (ESP_ERR_HW_BASE + 5)
#define ESP_ERR_HW_TIMEOUT             (ESP_ERR_HW_BASE + 6)
#define ESP_ERR_HW_BUSY                (ESP_ERR_HW_BASE + 7)

#ifdef __cplusplus
}
#endif

#endif // HARDWARE_TYPES_H
