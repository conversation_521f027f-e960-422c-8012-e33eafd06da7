/**
 * @file hal_interface.h
 * @brief 硬件抽象层(HAL)核心接口定义
 * 
 * 定义了硬件抽象层的统一接口，所有硬件驱动都需要实现这些接口
 */

#ifndef HAL_INTERFACE_H
#define HAL_INTERFACE_H

#include "hardware_types.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== HAL接口定义 ====================

/**
 * @brief HAL驱动注册信息
 */
typedef struct {
    const char* name;               // 驱动名称
    hardware_type_t type;           // 硬件类型
    uint32_t version;               // 驱动版本
    const hardware_ops_t* ops;      // 操作接口
} hal_driver_info_t;

/**
 * @brief HAL设备实例
 */
typedef struct hal_device {
    hardware_device_t base;         // 基础设备信息
    const hal_driver_info_t* driver; // 驱动信息
    struct hal_device* next;        // 链表指针
} hal_device_t;

// ==================== 显示设备HAL接口 ====================

/**
 * @brief 显示设备特定操作
 */
typedef struct {
    esp_err_t (*set_pixel)(hal_device_t* device, uint16_t x, uint16_t y, uint32_t color);
    esp_err_t (*fill_rect)(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, uint32_t color);
    esp_err_t (*flush)(hal_device_t* device, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const void* data);
    esp_err_t (*set_brightness)(hal_device_t* device, uint8_t brightness);
    esp_err_t (*power_on)(hal_device_t* device);
    esp_err_t (*power_off)(hal_device_t* device);
} display_hal_ops_t;

// ==================== 传感器HAL接口 ====================

/**
 * @brief 传感器特定操作
 */
typedef struct {
    esp_err_t (*read_data)(hal_device_t* device, sensor_data_t* data);
    esp_err_t (*set_sample_rate)(hal_device_t* device, uint32_t rate);
    esp_err_t (*calibrate)(hal_device_t* device);
    esp_err_t (*set_threshold)(hal_device_t* device, float threshold);
    esp_err_t (*enable_interrupt)(hal_device_t* device, bool enable);
} sensor_hal_ops_t;

// ==================== 执行器HAL接口 ====================

/**
 * @brief 执行器特定操作
 */
typedef struct {
    esp_err_t (*set_output)(hal_device_t* device, const actuator_data_t* data);
    esp_err_t (*get_status)(hal_device_t* device, actuator_data_t* status);
    esp_err_t (*enable)(hal_device_t* device, bool enable);
    esp_err_t (*reset)(hal_device_t* device);
} actuator_hal_ops_t;

// ==================== 连接设备HAL接口 ====================

/**
 * @brief 连接设备特定操作
 */
typedef struct {
    esp_err_t (*connect)(hal_device_t* device, const void* config);
    esp_err_t (*disconnect)(hal_device_t* device);
    esp_err_t (*get_status)(hal_device_t* device, connectivity_state_t* state);
    esp_err_t (*send_data)(hal_device_t* device, const void* data, size_t size);
    esp_err_t (*receive_data)(hal_device_t* device, void* data, size_t size, size_t* received);
} connectivity_hal_ops_t;

// ==================== HAL管理接口 ====================

/**
 * @brief 初始化HAL系统
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_init(void);

/**
 * @brief 反初始化HAL系统
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_deinit(void);

/**
 * @brief 注册HAL驱动
 * @param driver_info 驱动信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_register_driver(const hal_driver_info_t* driver_info);

/**
 * @brief 注销HAL驱动
 * @param name 驱动名称
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_unregister_driver(const char* name);

/**
 * @brief 创建设备实例
 * @param driver_name 驱动名称
 * @param device_id 设备ID
 * @param config 设备配置
 * @return 设备句柄，NULL表示失败
 */
hal_device_t* hal_create_device(const char* driver_name, hardware_id_t device_id, const void* config);

/**
 * @brief 销毁设备实例
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_destroy_device(hal_device_t* device);

/**
 * @brief 根据ID查找设备
 * @param device_id 设备ID
 * @return 设备句柄，NULL表示未找到
 */
hal_device_t* hal_find_device_by_id(hardware_id_t device_id);

/**
 * @brief 根据类型查找设备
 * @param type 硬件类型
 * @param index 索引（同类型设备的第几个）
 * @return 设备句柄，NULL表示未找到
 */
hal_device_t* hal_find_device_by_type(hardware_type_t type, uint32_t index);

/**
 * @brief 获取设备列表
 * @param type 硬件类型，HW_TYPE_UNKNOWN表示所有类型
 * @param devices 设备列表缓冲区
 * @param max_count 最大设备数量
 * @return 实际设备数量
 */
uint32_t hal_get_device_list(hardware_type_t type, hal_device_t** devices, uint32_t max_count);

// ==================== 设备操作接口 ====================

/**
 * @brief 初始化设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_init(hal_device_t* device);

/**
 * @brief 反初始化设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_deinit(hal_device_t* device);

/**
 * @brief 从设备读取数据
 * @param device 设备句柄
 * @param data 数据缓冲区
 * @param size 数据大小
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_read(hal_device_t* device, void* data, size_t size);

/**
 * @brief 向设备写入数据
 * @param device 设备句柄
 * @param data 数据缓冲区
 * @param size 数据大小
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_write(hal_device_t* device, const void* data, size_t size);

/**
 * @brief 控制设备
 * @param device 设备句柄
 * @param cmd 控制命令
 * @param arg 命令参数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_control(hal_device_t* device, uint32_t cmd, void* arg);

/**
 * @brief 获取设备状态
 * @param device 设备句柄
 * @return 设备状态
 */
hardware_state_t hal_device_get_state(hal_device_t* device);

/**
 * @brief 设置设备状态
 * @param device 设备句柄
 * @param state 新状态
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hal_device_set_state(hal_device_t* device, hardware_state_t state);

// ==================== 类型特定接口获取 ====================

/**
 * @brief 获取显示设备特定操作接口
 * @param device 设备句柄
 * @return 显示操作接口，NULL表示不是显示设备
 */
const display_hal_ops_t* hal_get_display_ops(hal_device_t* device);

/**
 * @brief 获取传感器特定操作接口
 * @param device 设备句柄
 * @return 传感器操作接口，NULL表示不是传感器
 */
const sensor_hal_ops_t* hal_get_sensor_ops(hal_device_t* device);

/**
 * @brief 获取执行器特定操作接口
 * @param device 设备句柄
 * @return 执行器操作接口，NULL表示不是执行器
 */
const actuator_hal_ops_t* hal_get_actuator_ops(hal_device_t* device);

/**
 * @brief 获取连接设备特定操作接口
 * @param device 设备句柄
 * @return 连接操作接口，NULL表示不是连接设备
 */
const connectivity_hal_ops_t* hal_get_connectivity_ops(hal_device_t* device);

#ifdef __cplusplus
}
#endif

#endif // HAL_INTERFACE_H
