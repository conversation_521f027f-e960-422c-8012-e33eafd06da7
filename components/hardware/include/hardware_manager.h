/**
 * @file hardware_manager.h
 * @brief 硬件管理器接口定义
 * 
 * 硬件管理器负责统一管理所有硬件设备，提供设备发现、配置、状态监控等功能
 */

#ifndef HARDWARE_MANAGER_H
#define HARDWARE_MANAGER_H

#include "hal_interface.h"
#include "hardware_types.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 硬件管理器配置 ====================

/**
 * @brief 硬件管理器配置
 */
typedef struct {
    uint32_t max_devices;           // 最大设备数量
    uint32_t scan_interval_ms;      // 设备扫描间隔
    bool auto_init_devices;         // 自动初始化设备
    bool enable_hot_plug;           // 支持热插拔
} hardware_manager_config_t;

/**
 * @brief 默认硬件管理器配置
 */
#define HARDWARE_MANAGER_DEFAULT_CONFIG() { \
    .max_devices = 32, \
    .scan_interval_ms = 5000, \
    .auto_init_devices = true, \
    .enable_hot_plug = false \
}

// ==================== 设备事件定义 ====================

/**
 * @brief 硬件设备事件类型
 */
typedef enum {
    HW_EVENT_DEVICE_ADDED = 0,      // 设备添加
    HW_EVENT_DEVICE_REMOVED,        // 设备移除
    HW_EVENT_DEVICE_INITIALIZED,    // 设备初始化完成
    HW_EVENT_DEVICE_ERROR,          // 设备错误
    HW_EVENT_DEVICE_STATE_CHANGED   // 设备状态改变
} hardware_event_type_t;

/**
 * @brief 硬件设备事件数据
 */
typedef struct {
    hardware_event_type_t type;
    hardware_id_t device_id;
    hardware_type_t device_type;
    hardware_state_t old_state;
    hardware_state_t new_state;
    esp_err_t error_code;
    void* user_data;
} hardware_event_data_t;

/**
 * @brief 硬件事件回调函数类型
 */
typedef void (*hardware_event_callback_t)(const hardware_event_data_t* event_data);

// ==================== 设备配置模板 ====================

/**
 * @brief 设备配置模板
 */
typedef struct {
    char name[32];                  // 配置名称
    hardware_type_t type;           // 硬件类型
    char driver_name[32];           // 驱动名称
    void* config_data;              // 配置数据
    size_t config_size;             // 配置数据大小
    bool auto_create;               // 自动创建设备
} device_config_template_t;

// ==================== 硬件管理器接口 ====================

/**
 * @brief 初始化硬件管理器
 * @param config 管理器配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_init(const hardware_manager_config_t* config);

/**
 * @brief 反初始化硬件管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_deinit(void);

/**
 * @brief 启动硬件管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_start(void);

/**
 * @brief 停止硬件管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_stop(void);

// ==================== 设备管理接口 ====================

/**
 * @brief 添加设备配置模板
 * @param template 设备配置模板
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_add_template(const device_config_template_t* template);

/**
 * @brief 移除设备配置模板
 * @param name 模板名称
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_remove_template(const char* name);

/**
 * @brief 根据模板创建设备
 * @param template_name 模板名称
 * @param device_id 设备ID
 * @return 设备句柄，NULL表示失败
 */
hal_device_t* hardware_manager_create_device_from_template(const char* template_name, hardware_id_t device_id);

/**
 * @brief 添加设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_add_device(hal_device_t* device);

/**
 * @brief 移除设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_remove_device(hardware_id_t device_id);

/**
 * @brief 查找设备
 * @param device_id 设备ID
 * @return 设备句柄，NULL表示未找到
 */
hal_device_t* hardware_manager_find_device(hardware_id_t device_id);

/**
 * @brief 获取设备列表
 * @param type 硬件类型，HW_TYPE_UNKNOWN表示所有类型
 * @param devices 设备列表缓冲区
 * @param max_count 最大设备数量
 * @return 实际设备数量
 */
uint32_t hardware_manager_get_devices(hardware_type_t type, hal_device_t** devices, uint32_t max_count);

/**
 * @brief 获取设备数量
 * @param type 硬件类型，HW_TYPE_UNKNOWN表示所有类型
 * @return 设备数量
 */
uint32_t hardware_manager_get_device_count(hardware_type_t type);

// ==================== 设备操作接口 ====================

/**
 * @brief 初始化设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_init_device(hardware_id_t device_id);

/**
 * @brief 反初始化设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_deinit_device(hardware_id_t device_id);

/**
 * @brief 启用设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_enable_device(hardware_id_t device_id);

/**
 * @brief 禁用设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_disable_device(hardware_id_t device_id);

/**
 * @brief 重置设备
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_reset_device(hardware_id_t device_id);

/**
 * @brief 获取设备状态
 * @param device_id 设备ID
 * @return 设备状态
 */
hardware_state_t hardware_manager_get_device_state(hardware_id_t device_id);

// ==================== 事件管理接口 ====================

/**
 * @brief 注册事件回调
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_register_event_callback(hardware_event_callback_t callback, void* user_data);

/**
 * @brief 注销事件回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_unregister_event_callback(hardware_event_callback_t callback);

/**
 * @brief 发送设备事件
 * @param event_data 事件数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_send_event(const hardware_event_data_t* event_data);

// ==================== 配置管理接口 ====================

/**
 * @brief 加载设备配置
 * @param config_file 配置文件路径
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_load_config(const char* config_file);

/**
 * @brief 保存设备配置
 * @param config_file 配置文件路径
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_save_config(const char* config_file);

/**
 * @brief 获取设备配置
 * @param device_id 设备ID
 * @param config 配置缓冲区
 * @param size 缓冲区大小
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_get_device_config(hardware_id_t device_id, void* config, size_t size);

/**
 * @brief 设置设备配置
 * @param device_id 设备ID
 * @param config 配置数据
 * @param size 配置数据大小
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_set_device_config(hardware_id_t device_id, const void* config, size_t size);

// ==================== 统计信息接口 ====================

/**
 * @brief 硬件管理器统计信息
 */
typedef struct {
    uint32_t total_devices;         // 总设备数
    uint32_t active_devices;        // 活跃设备数
    uint32_t error_devices;         // 错误设备数
    uint32_t events_sent;           // 发送的事件数
    uint32_t uptime_seconds;        // 运行时间（秒）
} hardware_manager_stats_t;

/**
 * @brief 获取硬件管理器统计信息
 * @param stats 统计信息缓冲区
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_get_stats(hardware_manager_stats_t* stats);

/**
 * @brief 重置统计信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t hardware_manager_reset_stats(void);

#ifdef __cplusplus
}
#endif

#endif // HARDWARE_MANAGER_H
