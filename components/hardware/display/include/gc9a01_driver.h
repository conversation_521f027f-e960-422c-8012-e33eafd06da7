/**
 * @file gc9a01_driver.h
 * @brief GC9A01圆形显示屏驱动接口
 * 
 * GC9A01是一款240x240分辨率的圆形TFT显示屏控制器
 */

#ifndef GC9A01_DRIVER_H
#define GC9A01_DRIVER_H

#include "display_hal.h"
#include "hal_interface.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== GC9A01常量定义 ====================

#define GC9A01_SCREEN_WIDTH         240
#define GC9A01_SCREEN_HEIGHT        240
#define GC9A01_COLOR_DEPTH          16
#define GC9A01_PIXEL_CLOCK_HZ       (20 * 1000 * 1000)  // 20MHz
#define GC9A01_SPI_QUEUE_SIZE       10

// ==================== GC9A01寄存器定义 ====================

#define GC9A01_CMD_SWRESET          0x01
#define GC9A01_CMD_RDDID            0x04
#define GC9A01_CMD_RDDST            0x09
#define GC9A01_CMD_SLPIN            0x10
#define GC9A01_CMD_SLPOUT           0x11
#define GC9A01_CMD_PTLON            0x12
#define GC9A01_CMD_NORON            0x13
#define GC9A01_CMD_INVOFF           0x20
#define GC9A01_CMD_INVON            0x21
#define GC9A01_CMD_DISPOFF          0x28
#define GC9A01_CMD_DISPON           0x29
#define GC9A01_CMD_CASET            0x2A
#define GC9A01_CMD_RASET            0x2B
#define GC9A01_CMD_RAMWR            0x2C
#define GC9A01_CMD_RAMRD            0x2E
#define GC9A01_CMD_PTLAR            0x30
#define GC9A01_CMD_VSCRDEF          0x33
#define GC9A01_CMD_MADCTL           0x36
#define GC9A01_CMD_VSCRSADD         0x37
#define GC9A01_CMD_PIXFMT           0x3A
#define GC9A01_CMD_WRDISBV          0x51
#define GC9A01_CMD_RDDISBV          0x52
#define GC9A01_CMD_WRCTRLD          0x53

// ==================== GC9A01配置结构 ====================

/**
 * @brief GC9A01 SPI配置
 */
typedef struct {
    int spi_host;                   // SPI主机
    int mosi_pin;                   // MOSI引脚
    int sclk_pin;                   // SCLK引脚
    int cs_pin;                     // CS引脚
    int dc_pin;                     // DC引脚
    int rst_pin;                    // RST引脚
    int backlight_pin;              // 背光引脚
    uint32_t pixel_clock_hz;        // 像素时钟频率
    size_t spi_queue_size;          // SPI队列大小
} gc9a01_spi_config_t;

/**
 * @brief GC9A01显示配置
 */
typedef struct {
    bool invert_color;              // 颜色反转
    bool swap_xy;                   // XY轴交换
    bool mirror_x;                  // X轴镜像
    bool mirror_y;                  // Y轴镜像
    uint8_t brightness;             // 亮度 (0-255)
    display_rotation_t rotation;    // 旋转角度
} gc9a01_display_config_t;

/**
 * @brief GC9A01设备配置
 */
typedef struct {
    gc9a01_spi_config_t spi;        // SPI配置
    gc9a01_display_config_t display; // 显示配置
    display_buffer_config_t buffer;  // 缓冲区配置
} gc9a01_config_t;

/**
 * @brief GC9A01设备私有数据
 */
typedef struct {
    esp_lcd_panel_io_handle_t io_handle;    // LCD IO句柄
    esp_lcd_panel_handle_t panel_handle;    // LCD面板句柄
    spi_device_handle_t spi_device;         // SPI设备句柄
    gc9a01_config_t config;                 // 设备配置
    display_flush_ready_cb_t flush_cb;      // 刷新回调
    display_error_cb_t error_cb;            // 错误回调
    bool is_initialized;                    // 初始化状态
    uint32_t frame_count;                   // 帧计数
    uint32_t error_count;                   // 错误计数
} gc9a01_device_t;

// ==================== GC9A01默认配置 ====================

/**
 * @brief GC9A01默认SPI配置
 */
#define GC9A01_DEFAULT_SPI_CONFIG() { \
    .spi_host = SPI2_HOST, \
    .mosi_pin = 20, \
    .sclk_pin = 19, \
    .cs_pin = -1, \
    .dc_pin = 21, \
    .rst_pin = 1, \
    .backlight_pin = -1, \
    .pixel_clock_hz = GC9A01_PIXEL_CLOCK_HZ, \
    .spi_queue_size = GC9A01_SPI_QUEUE_SIZE \
}

/**
 * @brief GC9A01默认显示配置
 */
#define GC9A01_DEFAULT_DISPLAY_CONFIG() { \
    .invert_color = false, \
    .swap_xy = false, \
    .mirror_x = false, \
    .mirror_y = false, \
    .brightness = 255, \
    .rotation = DISPLAY_ROTATION_0 \
}

/**
 * @brief GC9A01默认设备配置
 */
#define GC9A01_DEFAULT_CONFIG() { \
    .spi = GC9A01_DEFAULT_SPI_CONFIG(), \
    .display = GC9A01_DEFAULT_DISPLAY_CONFIG(), \
    .buffer = {0} \
}

// ==================== GC9A01驱动接口 ====================

/**
 * @brief 注册GC9A01驱动
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_register_driver(void);

/**
 * @brief 创建GC9A01设备
 * @param device_id 设备ID
 * @param config 设备配置
 * @return 设备句柄，NULL表示失败
 */
hal_device_t* gc9a01_create_device(hardware_id_t device_id, const gc9a01_config_t* config);

/**
 * @brief 销毁GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_destroy_device(hal_device_t* device);

/**
 * @brief 获取GC9A01私有数据
 * @param device 设备句柄
 * @return 私有数据指针，NULL表示失败
 */
gc9a01_device_t* gc9a01_get_private_data(hal_device_t* device);

// ==================== GC9A01硬件操作 ====================

/**
 * @brief 初始化GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_init(hal_device_t* device);

/**
 * @brief 反初始化GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_deinit(hal_device_t* device);

/**
 * @brief 上电GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_power_on(hal_device_t* device);

/**
 * @brief 断电GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_power_off(hal_device_t* device);

/**
 * @brief 重置GC9A01设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_reset(hal_device_t* device);

// ==================== GC9A01显示操作 ====================

/**
 * @brief 清屏
 * @param device 设备句柄
 * @param color 清屏颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_clear(hal_device_t* device, uint32_t color);

/**
 * @brief 设置像素
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param color 像素颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_pixel(hal_device_t* device, uint16_t x, uint16_t y, uint32_t color);

/**
 * @brief 填充矩形
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 填充颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_fill_rect(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, uint32_t color);

/**
 * @brief 绘制位图
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param data 位图数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_draw_bitmap(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, const void* data);

/**
 * @brief 刷新显示
 * @param device 设备句柄
 * @param x1 起始X坐标
 * @param y1 起始Y坐标
 * @param x2 结束X坐标
 * @param y2 结束Y坐标
 * @param data 显示数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_flush(hal_device_t* device, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const void* data);

// ==================== GC9A01配置操作 ====================

/**
 * @brief 设置亮度
 * @param device 设备句柄
 * @param brightness 亮度值 (0-255)
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_brightness(hal_device_t* device, uint8_t brightness);

/**
 * @brief 设置旋转角度
 * @param device 设备句柄
 * @param rotation 旋转角度
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_rotation(hal_device_t* device, display_rotation_t rotation);

/**
 * @brief 设置颜色反转
 * @param device 设备句柄
 * @param invert 是否反转
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_invert(hal_device_t* device, bool invert);

/**
 * @brief 设置显示窗口
 * @param device 设备句柄
 * @param x1 起始X坐标
 * @param y1 起始Y坐标
 * @param x2 结束X坐标
 * @param y2 结束Y坐标
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_window(hal_device_t* device, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2);

// ==================== GC9A01状态查询 ====================

/**
 * @brief 获取设备状态
 * @param device 设备句柄
 * @param status 状态输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_get_status(hal_device_t* device, display_status_t* status);

/**
 * @brief 获取分辨率
 * @param device 设备句柄
 * @param width 宽度输出
 * @param height 高度输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_get_resolution(hal_device_t* device, uint16_t* width, uint16_t* height);

// ==================== GC9A01回调设置 ====================

/**
 * @brief 设置刷新完成回调
 * @param device 设备句柄
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_flush_ready_cb(hal_device_t* device, display_flush_ready_cb_t callback);

/**
 * @brief 设置错误回调
 * @param device 设备句柄
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t gc9a01_set_error_cb(hal_device_t* device, display_error_cb_t callback);

#ifdef __cplusplus
}
#endif

#endif // GC9A01_DRIVER_H
