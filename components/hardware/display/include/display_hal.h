/**
 * @file display_hal.h
 * @brief 显示设备硬件抽象层接口
 * 
 * 定义了显示设备的统一接口，支持各种类型的显示屏
 */

#ifndef DISPLAY_HAL_H
#define DISPLAY_HAL_H

#include "hal_interface.h"
#include "hardware_types.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 显示设备常量定义 ====================

#define DISPLAY_MAX_WIDTH           480
#define DISPLAY_MAX_HEIGHT          480
#define DISPLAY_MAX_COLOR_DEPTH     32
#define DISPLAY_MAX_BRIGHTNESS      255

// ==================== 显示设备控制命令 ====================

#define DISPLAY_CMD_SET_BRIGHTNESS  0x1001
#define DISPLAY_CMD_SET_ROTATION    0x1002
#define DISPLAY_CMD_SET_INVERT      0x1003
#define DISPLAY_CMD_POWER_ON        0x1004
#define DISPLAY_CMD_POWER_OFF       0x1005
#define DISPLAY_CMD_RESET           0x1006
#define DISPLAY_CMD_CLEAR           0x1007
#define DISPLAY_CMD_FLUSH           0x1008

// ==================== 显示设备扩展配置 ====================

/**
 * @brief 显示旋转角度
 */
typedef enum {
    DISPLAY_ROTATION_0 = 0,
    DISPLAY_ROTATION_90,
    DISPLAY_ROTATION_180,
    DISPLAY_ROTATION_270
} display_rotation_t;

/**
 * @brief 显示颜色格式
 */
typedef enum {
    DISPLAY_COLOR_FORMAT_RGB565 = 0,
    DISPLAY_COLOR_FORMAT_RGB888,
    DISPLAY_COLOR_FORMAT_ARGB8888,
    DISPLAY_COLOR_FORMAT_MONO
} display_color_format_t;

/**
 * @brief 显示区域
 */
typedef struct {
    uint16_t x;
    uint16_t y;
    uint16_t width;
    uint16_t height;
} display_area_t;

/**
 * @brief 显示缓冲区配置
 */
typedef struct {
    void* buffer1;                  // 主缓冲区
    void* buffer2;                  // 副缓冲区（双缓冲）
    size_t buffer_size;             // 缓冲区大小
    bool double_buffer;             // 是否使用双缓冲
    uint16_t buffer_height;         // 缓冲区高度（行数）
} display_buffer_config_t;

/**
 * @brief 显示设备扩展配置
 */
typedef struct {
    display_config_t base;          // 基础配置
    display_rotation_t rotation;    // 旋转角度
    display_color_format_t format;  // 颜色格式
    display_buffer_config_t buffer; // 缓冲区配置
    bool invert_colors;             // 颜色反转
    uint8_t brightness;             // 亮度
    uint32_t refresh_rate;          // 刷新率 (Hz)
} display_extended_config_t;

// ==================== 显示设备状态信息 ====================

/**
 * @brief 显示设备状态
 */
typedef struct {
    bool is_powered;                // 是否上电
    bool is_initialized;            // 是否已初始化
    uint8_t current_brightness;     // 当前亮度
    display_rotation_t rotation;    // 当前旋转角度
    uint32_t frame_count;           // 帧计数
    uint32_t error_count;           // 错误计数
} display_status_t;

// ==================== 显示设备回调函数 ====================

/**
 * @brief 显示刷新完成回调
 */
typedef void (*display_flush_ready_cb_t)(hal_device_t* device);

/**
 * @brief 显示错误回调
 */
typedef void (*display_error_cb_t)(hal_device_t* device, esp_err_t error);

// ==================== 显示设备HAL接口 ====================

/**
 * @brief 显示设备HAL操作接口
 */
typedef struct {
    // 基础操作
    esp_err_t (*init)(hal_device_t* device);
    esp_err_t (*deinit)(hal_device_t* device);
    esp_err_t (*power_on)(hal_device_t* device);
    esp_err_t (*power_off)(hal_device_t* device);
    esp_err_t (*reset)(hal_device_t* device);
    
    // 显示操作
    esp_err_t (*clear)(hal_device_t* device, uint32_t color);
    esp_err_t (*set_pixel)(hal_device_t* device, uint16_t x, uint16_t y, uint32_t color);
    esp_err_t (*fill_rect)(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, uint32_t color);
    esp_err_t (*draw_bitmap)(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, const void* data);
    esp_err_t (*flush)(hal_device_t* device, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const void* data);
    
    // 配置操作
    esp_err_t (*set_brightness)(hal_device_t* device, uint8_t brightness);
    esp_err_t (*set_rotation)(hal_device_t* device, display_rotation_t rotation);
    esp_err_t (*set_invert)(hal_device_t* device, bool invert);
    esp_err_t (*set_window)(hal_device_t* device, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2);
    
    // 状态查询
    esp_err_t (*get_status)(hal_device_t* device, display_status_t* status);
    esp_err_t (*get_resolution)(hal_device_t* device, uint16_t* width, uint16_t* height);
    
    // 回调设置
    esp_err_t (*set_flush_ready_cb)(hal_device_t* device, display_flush_ready_cb_t callback);
    esp_err_t (*set_error_cb)(hal_device_t* device, display_error_cb_t callback);
} display_hal_interface_t;

// ==================== 显示设备管理接口 ====================

/**
 * @brief 注册显示设备驱动
 * @param driver_info 驱动信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_register_driver(const hal_driver_info_t* driver_info);

/**
 * @brief 创建显示设备
 * @param driver_name 驱动名称
 * @param device_id 设备ID
 * @param config 设备配置
 * @return 设备句柄，NULL表示失败
 */
hal_device_t* display_hal_create_device(const char* driver_name, hardware_id_t device_id, 
                                       const display_extended_config_t* config);

/**
 * @brief 销毁显示设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_destroy_device(hal_device_t* device);

/**
 * @brief 获取显示设备接口
 * @param device 设备句柄
 * @return 显示接口，NULL表示不是显示设备
 */
const display_hal_interface_t* display_hal_get_interface(hal_device_t* device);

// ==================== 便捷操作接口 ====================

/**
 * @brief 初始化显示设备
 * @param device 设备句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_init(hal_device_t* device);

/**
 * @brief 清屏
 * @param device 设备句柄
 * @param color 清屏颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_clear(hal_device_t* device, uint32_t color);

/**
 * @brief 设置像素
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param color 像素颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_set_pixel(hal_device_t* device, uint16_t x, uint16_t y, uint32_t color);

/**
 * @brief 填充矩形
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 填充颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_fill_rect(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, uint32_t color);

/**
 * @brief 绘制位图
 * @param device 设备句柄
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param data 位图数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_draw_bitmap(hal_device_t* device, uint16_t x, uint16_t y, uint16_t w, uint16_t h, const void* data);

/**
 * @brief 刷新显示
 * @param device 设备句柄
 * @param area 刷新区域，NULL表示全屏
 * @param data 显示数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_flush(hal_device_t* device, const display_area_t* area, const void* data);

/**
 * @brief 设置亮度
 * @param device 设备句柄
 * @param brightness 亮度值 (0-255)
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_set_brightness(hal_device_t* device, uint8_t brightness);

/**
 * @brief 设置旋转角度
 * @param device 设备句柄
 * @param rotation 旋转角度
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_set_rotation(hal_device_t* device, display_rotation_t rotation);

/**
 * @brief 获取显示分辨率
 * @param device 设备句柄
 * @param width 宽度输出
 * @param height 高度输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_get_resolution(hal_device_t* device, uint16_t* width, uint16_t* height);

/**
 * @brief 获取显示状态
 * @param device 设备句柄
 * @param status 状态输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_hal_get_status(hal_device_t* device, display_status_t* status);

// ==================== 颜色转换工具 ====================

/**
 * @brief RGB转换为16位颜色
 * @param r 红色分量 (0-255)
 * @param g 绿色分量 (0-255)
 * @param b 蓝色分量 (0-255)
 * @return 16位颜色值
 */
uint16_t display_hal_rgb_to_color16(uint8_t r, uint8_t g, uint8_t b);

/**
 * @brief RGB转换为24位颜色
 * @param r 红色分量 (0-255)
 * @param g 绿色分量 (0-255)
 * @param b 蓝色分量 (0-255)
 * @return 24位颜色值
 */
uint32_t display_hal_rgb_to_color24(uint8_t r, uint8_t g, uint8_t b);

/**
 * @brief 16位颜色转换为RGB
 * @param color 16位颜色值
 * @param r 红色分量输出
 * @param g 绿色分量输出
 * @param b 蓝色分量输出
 */
void display_hal_color16_to_rgb(uint16_t color, uint8_t* r, uint8_t* g, uint8_t* b);

#ifdef __cplusplus
}
#endif

#endif // DISPLAY_HAL_H
