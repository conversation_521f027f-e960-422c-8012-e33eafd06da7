/**
 * @file message_router.h
 * @brief 消息路由器接口定义
 * 
 * 消息路由器负责在Agent之间路由消息，支持点对点、广播、组播等多种路由模式
 */

#ifndef MESSAGE_ROUTER_H
#define MESSAGE_ROUTER_H

#include "agent_interface.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 路由器配置 ====================

/**
 * @brief 消息路由器配置
 */
typedef struct {
    uint32_t queue_size;            // 路由队列大小
    uint32_t max_routes;            // 最大路由数量
    uint32_t worker_stack_size;     // 工作任务堆栈大小
    uint32_t worker_priority;       // 工作任务优先级
    uint32_t max_retry_count;       // 最大重试次数
    uint32_t retry_delay_ms;        // 重试延迟
    bool enable_logging;            // 启用日志
    bool enable_stats;              // 启用统计
} message_router_config_t;

/**
 * @brief 默认消息路由器配置
 */
#define MESSAGE_ROUTER_DEFAULT_CONFIG() { \
    .queue_size = 32, \
    .max_routes = 64, \
    .worker_stack_size = 4096, \
    .worker_priority = 5, \
    .max_retry_count = 3, \
    .retry_delay_ms = 100, \
    .enable_logging = true, \
    .enable_stats = true \
}

// ==================== 路由类型定义 ====================

/**
 * @brief 路由类型枚举
 */
typedef enum {
    ROUTE_TYPE_DIRECT = 0,          // 直接路由（点对点）
    ROUTE_TYPE_BROADCAST,           // 广播路由
    ROUTE_TYPE_MULTICAST,           // 组播路由
    ROUTE_TYPE_ANYCAST,             // 任播路由（发送给类型中的任一Agent）
    ROUTE_TYPE_CONDITIONAL          // 条件路由
} route_type_t;

/**
 * @brief 路由策略枚举
 */
typedef enum {
    ROUTE_STRATEGY_FIFO = 0,        // 先进先出
    ROUTE_STRATEGY_PRIORITY,        // 优先级
    ROUTE_STRATEGY_ROUND_ROBIN,     // 轮询
    ROUTE_STRATEGY_LOAD_BALANCE     // 负载均衡
} route_strategy_t;

/**
 * @brief 路由状态枚举
 */
typedef enum {
    ROUTE_STATUS_PENDING = 0,       // 等待中
    ROUTE_STATUS_ROUTING,           // 路由中
    ROUTE_STATUS_DELIVERED,         // 已投递
    ROUTE_STATUS_FAILED,            // 失败
    ROUTE_STATUS_TIMEOUT,           // 超时
    ROUTE_STATUS_DROPPED            // 丢弃
} route_status_t;

// ==================== 路由规则定义 ====================

/**
 * @brief 路由条件函数类型
 */
typedef bool (*route_condition_func_t)(const agent_message_t* message, agent_handle_t agent);

/**
 * @brief 路由规则
 */
typedef struct {
    uint32_t rule_id;               // 规则ID
    route_type_t type;              // 路由类型
    route_strategy_t strategy;      // 路由策略
    agent_type_t target_type;       // 目标Agent类型
    agent_id_t target_id;           // 目标Agent ID
    message_type_t message_type;    // 消息类型过滤
    uint32_t command_filter;        // 命令过滤
    message_priority_t min_priority; // 最小优先级
    route_condition_func_t condition; // 条件函数
    bool enabled;                   // 是否启用
} route_rule_t;

/**
 * @brief 路由记录
 */
typedef struct {
    uint32_t route_id;              // 路由ID
    agent_message_t* message;       // 消息
    route_type_t type;              // 路由类型
    route_status_t status;          // 路由状态
    agent_id_t sender_id;           // 发送者ID
    agent_id_t* target_ids;         // 目标ID列表
    uint32_t target_count;          // 目标数量
    uint32_t delivered_count;       // 已投递数量
    uint32_t retry_count;           // 重试次数
    uint32_t timestamp;             // 时间戳
    uint32_t timeout_ms;            // 超时时间
} route_record_t;

// ==================== 路由统计信息 ====================

/**
 * @brief 消息路由器统计信息
 */
typedef struct {
    uint32_t total_messages;        // 总消息数
    uint32_t routed_messages;       // 已路由消息数
    uint32_t failed_messages;       // 失败消息数
    uint32_t dropped_messages;      // 丢弃消息数
    uint32_t timeout_messages;      // 超时消息数
    uint32_t retry_count;           // 重试次数
    uint32_t active_routes;         // 活跃路由数
    uint32_t total_routes;          // 总路由数
    uint32_t queue_usage;           // 队列使用率
    uint32_t avg_route_time_ms;     // 平均路由时间
} message_router_stats_t;

// ==================== 路由器事件定义 ====================

/**
 * @brief 路由器事件类型
 */
typedef enum {
    ROUTER_EVENT_MESSAGE_RECEIVED = 0,  // 消息接收
    ROUTER_EVENT_MESSAGE_ROUTED,        // 消息路由
    ROUTER_EVENT_MESSAGE_DELIVERED,     // 消息投递
    ROUTER_EVENT_MESSAGE_FAILED,        // 消息失败
    ROUTER_EVENT_MESSAGE_TIMEOUT,       // 消息超时
    ROUTER_EVENT_ROUTE_ADDED,           // 路由添加
    ROUTER_EVENT_ROUTE_REMOVED,         // 路由移除
    ROUTER_EVENT_QUEUE_FULL             // 队列满
} router_event_type_t;

/**
 * @brief 路由器事件数据
 */
typedef struct {
    router_event_type_t type;
    uint32_t route_id;
    const agent_message_t* message;
    route_status_t status;
    esp_err_t error_code;
    void* user_data;
} router_event_data_t;

/**
 * @brief 路由器事件回调函数类型
 */
typedef void (*router_event_callback_t)(const router_event_data_t* event_data);

// ==================== 消息路由器接口 ====================

/**
 * @brief 初始化消息路由器
 * @param config 路由器配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_init(const message_router_config_t* config);

/**
 * @brief 反初始化消息路由器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_deinit(void);

/**
 * @brief 启动消息路由器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_start(void);

/**
 * @brief 停止消息路由器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_stop(void);

// ==================== 路由规则管理 ====================

/**
 * @brief 添加路由规则
 * @param rule 路由规则
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_add_rule(const route_rule_t* rule);

/**
 * @brief 移除路由规则
 * @param rule_id 规则ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_remove_rule(uint32_t rule_id);

/**
 * @brief 启用路由规则
 * @param rule_id 规则ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_enable_rule(uint32_t rule_id);

/**
 * @brief 禁用路由规则
 * @param rule_id 规则ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_disable_rule(uint32_t rule_id);

/**
 * @brief 获取路由规则
 * @param rule_id 规则ID
 * @param rule 规则输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_get_rule(uint32_t rule_id, route_rule_t* rule);

/**
 * @brief 清除所有路由规则
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_clear_rules(void);

// ==================== 消息路由接口 ====================

/**
 * @brief 路由消息
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_route_message(const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 直接发送消息
 * @param target_id 目标Agent ID
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_send_direct(agent_id_t target_id, const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 广播消息
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_broadcast(const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 组播消息
 * @param target_type Agent类型
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_multicast(agent_type_t target_type, const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 任播消息
 * @param target_type Agent类型
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_anycast(agent_type_t target_type, const agent_message_t* message, uint32_t timeout_ms);

// ==================== 路由查询接口 ====================

/**
 * @brief 获取路由记录
 * @param route_id 路由ID
 * @param record 路由记录输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_get_route_record(uint32_t route_id, route_record_t* record);

/**
 * @brief 获取活跃路由列表
 * @param routes 路由记录列表
 * @param max_count 最大数量
 * @return 实际路由数量
 */
uint32_t message_router_get_active_routes(route_record_t* routes, uint32_t max_count);

/**
 * @brief 取消路由
 * @param route_id 路由ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_cancel_route(uint32_t route_id);

// ==================== 事件管理接口 ====================

/**
 * @brief 注册事件回调
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_register_event_callback(router_event_callback_t callback, void* user_data);

/**
 * @brief 注销事件回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_unregister_event_callback(router_event_callback_t callback);

// ==================== 统计信息接口 ====================

/**
 * @brief 获取路由器统计信息
 * @param stats 统计信息输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_get_stats(message_router_stats_t* stats);

/**
 * @brief 重置统计信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t message_router_reset_stats(void);

// ==================== 调试接口 ====================

/**
 * @brief 打印路由规则
 */
void message_router_print_rules(void);

/**
 * @brief 打印活跃路由
 */
void message_router_print_active_routes(void);

/**
 * @brief 打印统计信息
 */
void message_router_print_stats(void);

// ==================== 便捷宏定义 ====================

/**
 * @brief 创建直接路由规则
 */
#define MESSAGE_ROUTER_CREATE_DIRECT_RULE(rule_id, target_id) { \
    .rule_id = rule_id, \
    .type = ROUTE_TYPE_DIRECT, \
    .strategy = ROUTE_STRATEGY_FIFO, \
    .target_type = AGENT_TYPE_UNKNOWN, \
    .target_id = target_id, \
    .message_type = MSG_TYPE_COMMAND, \
    .command_filter = 0, \
    .min_priority = MSG_PRIORITY_LOW, \
    .condition = NULL, \
    .enabled = true \
}

/**
 * @brief 创建广播路由规则
 */
#define MESSAGE_ROUTER_CREATE_BROADCAST_RULE(rule_id) { \
    .rule_id = rule_id, \
    .type = ROUTE_TYPE_BROADCAST, \
    .strategy = ROUTE_STRATEGY_FIFO, \
    .target_type = AGENT_TYPE_UNKNOWN, \
    .target_id = 0, \
    .message_type = MSG_TYPE_EVENT, \
    .command_filter = 0, \
    .min_priority = MSG_PRIORITY_LOW, \
    .condition = NULL, \
    .enabled = true \
}

/**
 * @brief 创建组播路由规则
 */
#define MESSAGE_ROUTER_CREATE_MULTICAST_RULE(rule_id, target_type) { \
    .rule_id = rule_id, \
    .type = ROUTE_TYPE_MULTICAST, \
    .strategy = ROUTE_STRATEGY_FIFO, \
    .target_type = target_type, \
    .target_id = 0, \
    .message_type = MSG_TYPE_COMMAND, \
    .command_filter = 0, \
    .min_priority = MSG_PRIORITY_LOW, \
    .condition = NULL, \
    .enabled = true \
}

#ifdef __cplusplus
}
#endif

#endif // MESSAGE_ROUTER_H
