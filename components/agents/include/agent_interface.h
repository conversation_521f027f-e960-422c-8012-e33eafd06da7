/**
 * @file agent_interface.h
 * @brief Agent系统核心接口定义
 * 
 * 定义了Agent系统的统一接口，所有Agent都需要实现这些接口
 */

#ifndef AGENT_INTERFACE_H
#define AGENT_INTERFACE_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== Agent类型定义 ====================

/**
 * @brief Agent类型枚举
 */
typedef enum {
    AGENT_TYPE_UNKNOWN = 0,
    AGENT_TYPE_SYSTEM,          // 系统管理Agent
    AGENT_TYPE_DISPLAY,         // 显示控制Agent
    AGENT_TYPE_SENSOR,          // 传感器Agent
    AGENT_TYPE_CONTROL,         // 控制Agent
    AGENT_TYPE_COMMUNICATION,   // 通信Agent
    AGENT_TYPE_USER_DEFINED     // 用户自定义Agent
} agent_type_t;

/**
 * @brief Agent状态枚举
 */
typedef enum {
    AGENT_STATE_UNINITIALIZED = 0,
    AGENT_STATE_INITIALIZING,
    AGENT_STATE_READY,
    AGENT_STATE_RUNNING,
    AGENT_STATE_PAUSED,
    AGENT_STATE_ERROR,
    AGENT_STATE_STOPPING,
    AGENT_STATE_STOPPED
} agent_state_t;

/**
 * @brief Agent优先级枚举
 */
typedef enum {
    AGENT_PRIORITY_LOW = 1,
    AGENT_PRIORITY_NORMAL = 3,
    AGENT_PRIORITY_HIGH = 5,
    AGENT_PRIORITY_CRITICAL = 7
} agent_priority_t;

/**
 * @brief Agent ID类型
 */
typedef uint32_t agent_id_t;

/**
 * @brief Agent句柄类型
 */
typedef struct agent_instance* agent_handle_t;

// ==================== 消息系统定义 ====================

/**
 * @brief 消息类型枚举
 */
typedef enum {
    MSG_TYPE_COMMAND = 0,       // 命令消息
    MSG_TYPE_EVENT,             // 事件消息
    MSG_TYPE_DATA,              // 数据消息
    MSG_TYPE_REQUEST,           // 请求消息
    MSG_TYPE_RESPONSE,          // 响应消息
    MSG_TYPE_NOTIFICATION       // 通知消息
} message_type_t;

/**
 * @brief 消息优先级枚举
 */
typedef enum {
    MSG_PRIORITY_LOW = 0,
    MSG_PRIORITY_NORMAL,
    MSG_PRIORITY_HIGH,
    MSG_PRIORITY_URGENT
} message_priority_t;

/**
 * @brief Agent消息结构
 */
typedef struct {
    uint32_t id;                    // 消息ID
    message_type_t type;            // 消息类型
    message_priority_t priority;    // 消息优先级
    agent_id_t sender_id;           // 发送者ID
    agent_id_t receiver_id;         // 接收者ID
    uint32_t command;               // 命令/事件代码
    void* data;                     // 消息数据
    size_t data_size;               // 数据大小
    uint32_t timestamp;             // 时间戳
    bool need_response;             // 是否需要响应
    uint32_t timeout_ms;            // 超时时间
} agent_message_t;

// ==================== Agent配置定义 ====================

/**
 * @brief Agent配置结构
 */
typedef struct {
    agent_id_t id;                  // Agent ID
    agent_type_t type;              // Agent类型
    agent_priority_t priority;      // Agent优先级
    char name[32];                  // Agent名称
    char description[64];           // Agent描述
    uint32_t stack_size;            // 任务堆栈大小
    uint32_t queue_size;            // 消息队列大小
    uint32_t max_message_size;      // 最大消息大小
    bool auto_start;                // 自动启动
    void* private_config;           // 私有配置数据
    size_t private_config_size;     // 私有配置大小
} agent_config_t;

/**
 * @brief Agent统计信息
 */
typedef struct {
    uint32_t messages_received;     // 接收消息数
    uint32_t messages_sent;         // 发送消息数
    uint32_t messages_processed;    // 处理消息数
    uint32_t messages_dropped;      // 丢弃消息数
    uint32_t errors_count;          // 错误计数
    uint32_t uptime_seconds;        // 运行时间
    uint32_t cpu_usage_percent;     // CPU使用率
    uint32_t memory_usage_bytes;    // 内存使用量
} agent_stats_t;

// ==================== Agent回调函数类型 ====================

/**
 * @brief Agent初始化回调
 */
typedef esp_err_t (*agent_init_func_t)(agent_handle_t agent);

/**
 * @brief Agent反初始化回调
 */
typedef esp_err_t (*agent_deinit_func_t)(agent_handle_t agent);

/**
 * @brief Agent启动回调
 */
typedef esp_err_t (*agent_start_func_t)(agent_handle_t agent);

/**
 * @brief Agent停止回调
 */
typedef esp_err_t (*agent_stop_func_t)(agent_handle_t agent);

/**
 * @brief Agent消息处理回调
 */
typedef esp_err_t (*agent_message_handler_t)(agent_handle_t agent, const agent_message_t* message);

/**
 * @brief Agent状态变化回调
 */
typedef void (*agent_state_change_callback_t)(agent_handle_t agent, agent_state_t old_state, agent_state_t new_state);

/**
 * @brief Agent错误回调
 */
typedef void (*agent_error_callback_t)(agent_handle_t agent, esp_err_t error_code, const char* error_msg);

// ==================== Agent操作接口 ====================

/**
 * @brief Agent操作接口结构
 */
typedef struct {
    agent_init_func_t init;                     // 初始化函数
    agent_deinit_func_t deinit;                 // 反初始化函数
    agent_start_func_t start;                   // 启动函数
    agent_stop_func_t stop;                     // 停止函数
    agent_message_handler_t handle_message;     // 消息处理函数
} agent_ops_t;

// ==================== Agent实例结构 ====================

/**
 * @brief Agent实例结构
 */
typedef struct agent_instance {
    agent_config_t config;              // Agent配置
    agent_state_t state;                // Agent状态
    const agent_ops_t* ops;             // 操作接口
    TaskHandle_t task_handle;           // 任务句柄
    QueueHandle_t message_queue;        // 消息队列
    agent_stats_t stats;                // 统计信息
    void* private_data;                 // 私有数据
    
    // 回调函数
    agent_state_change_callback_t state_callback;
    agent_error_callback_t error_callback;
    
    // 链表指针
    struct agent_instance* next;
} agent_instance_t;

// ==================== Agent管理接口 ====================

/**
 * @brief 创建Agent实例
 * @param config Agent配置
 * @param ops Agent操作接口
 * @return Agent句柄，NULL表示失败
 */
agent_handle_t agent_create(const agent_config_t* config, const agent_ops_t* ops);

/**
 * @brief 销毁Agent实例
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_destroy(agent_handle_t agent);

/**
 * @brief 初始化Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_init(agent_handle_t agent);

/**
 * @brief 反初始化Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_deinit(agent_handle_t agent);

/**
 * @brief 启动Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_start(agent_handle_t agent);

/**
 * @brief 停止Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_stop(agent_handle_t agent);

/**
 * @brief 暂停Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_pause(agent_handle_t agent);

/**
 * @brief 恢复Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_resume(agent_handle_t agent);

// ==================== 消息传递接口 ====================

/**
 * @brief 发送消息给Agent
 * @param agent Agent句柄
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_send_message(agent_handle_t agent, const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 广播消息给所有Agent
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_broadcast_message(const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 发送消息给指定类型的Agent
 * @param type Agent类型
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_send_message_to_type(agent_type_t type, const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 创建消息
 * @param type 消息类型
 * @param command 命令代码
 * @param data 数据
 * @param data_size 数据大小
 * @return 消息指针，NULL表示失败
 */
agent_message_t* agent_create_message(message_type_t type, uint32_t command, const void* data, size_t data_size);

/**
 * @brief 销毁消息
 * @param message 消息指针
 */
void agent_destroy_message(agent_message_t* message);

// ==================== Agent状态查询接口 ====================

/**
 * @brief 获取Agent状态
 * @param agent Agent句柄
 * @return Agent状态
 */
agent_state_t agent_get_state(agent_handle_t agent);

/**
 * @brief 获取Agent配置
 * @param agent Agent句柄
 * @return Agent配置指针
 */
const agent_config_t* agent_get_config(agent_handle_t agent);

/**
 * @brief 获取Agent统计信息
 * @param agent Agent句柄
 * @param stats 统计信息输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_get_stats(agent_handle_t agent, agent_stats_t* stats);

/**
 * @brief 重置Agent统计信息
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_reset_stats(agent_handle_t agent);

// ==================== Agent回调设置接口 ====================

/**
 * @brief 设置状态变化回调
 * @param agent Agent句柄
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_set_state_callback(agent_handle_t agent, agent_state_change_callback_t callback);

/**
 * @brief 设置错误回调
 * @param agent Agent句柄
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_set_error_callback(agent_handle_t agent, agent_error_callback_t callback);

// ==================== 便捷宏定义 ====================

/**
 * @brief 默认Agent配置
 */
#define AGENT_DEFAULT_CONFIG(agent_id, agent_type, agent_name) { \
    .id = agent_id, \
    .type = agent_type, \
    .priority = AGENT_PRIORITY_NORMAL, \
    .name = agent_name, \
    .description = "", \
    .stack_size = 4096, \
    .queue_size = 10, \
    .max_message_size = 256, \
    .auto_start = true, \
    .private_config = NULL, \
    .private_config_size = 0 \
}

/**
 * @brief 创建简单消息
 */
#define AGENT_CREATE_SIMPLE_MESSAGE(msg_type, cmd) \
    agent_create_message(msg_type, cmd, NULL, 0)

/**
 * @brief 发送简单命令
 */
#define AGENT_SEND_COMMAND(agent, cmd) \
    do { \
        agent_message_t* msg = AGENT_CREATE_SIMPLE_MESSAGE(MSG_TYPE_COMMAND, cmd); \
        if (msg) { \
            agent_send_message(agent, msg, 1000); \
            agent_destroy_message(msg); \
        } \
    } while(0)

#ifdef __cplusplus
}
#endif

#endif // AGENT_INTERFACE_H
