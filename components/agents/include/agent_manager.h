/**
 * @file agent_manager.h
 * @brief Agent管理器接口定义
 * 
 * Agent管理器负责统一管理所有Agent实例，提供Agent注册、启动、停止、消息路由等功能
 */

#ifndef AGENT_MANAGER_H
#define AGENT_MANAGER_H

#include "agent_interface.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== Agent管理器配置 ====================

/**
 * @brief Agent管理器配置
 */
typedef struct {
    uint32_t max_agents;            // 最大Agent数量
    uint32_t message_pool_size;     // 消息池大小
    uint32_t router_queue_size;     // 路由器队列大小
    uint32_t router_stack_size;     // 路由器任务堆栈大小
    uint32_t monitor_interval_ms;   // 监控间隔
    bool enable_stats;              // 启用统计
    bool enable_watchdog;           // 启用看门狗
} agent_manager_config_t;

/**
 * @brief 默认Agent管理器配置
 */
#define AGENT_MANAGER_DEFAULT_CONFIG() { \
    .max_agents = 16, \
    .message_pool_size = 64, \
    .router_queue_size = 32, \
    .router_stack_size = 4096, \
    .monitor_interval_ms = 1000, \
    .enable_stats = true, \
    .enable_watchdog = true \
}

// ==================== Agent事件定义 ====================

/**
 * @brief Agent事件类型
 */
typedef enum {
    AGENT_EVENT_REGISTERED = 0,     // Agent注册
    AGENT_EVENT_UNREGISTERED,       // Agent注销
    AGENT_EVENT_STARTED,            // Agent启动
    AGENT_EVENT_STOPPED,            // Agent停止
    AGENT_EVENT_STATE_CHANGED,      // Agent状态改变
    AGENT_EVENT_ERROR,              // Agent错误
    AGENT_EVENT_MESSAGE_SENT,       // 消息发送
    AGENT_EVENT_MESSAGE_RECEIVED    // 消息接收
} agent_event_type_t;

/**
 * @brief Agent事件数据
 */
typedef struct {
    agent_event_type_t type;
    agent_id_t agent_id;
    agent_type_t agent_type;
    agent_state_t old_state;
    agent_state_t new_state;
    esp_err_t error_code;
    const char* error_msg;
    const agent_message_t* message;
    void* user_data;
} agent_event_data_t;

/**
 * @brief Agent事件回调函数类型
 */
typedef void (*agent_manager_event_callback_t)(const agent_event_data_t* event_data);

// ==================== Agent注册信息 ====================

/**
 * @brief Agent注册信息
 */
typedef struct {
    const char* name;               // Agent名称
    agent_type_t type;              // Agent类型
    const agent_ops_t* ops;         // 操作接口
    const agent_config_t* default_config; // 默认配置
    uint32_t version;               // 版本号
} agent_registration_info_t;

// ==================== Agent管理器统计信息 ====================

/**
 * @brief Agent管理器统计信息
 */
typedef struct {
    uint32_t total_agents;          // 总Agent数
    uint32_t running_agents;        // 运行中Agent数
    uint32_t error_agents;          // 错误Agent数
    uint32_t total_messages;        // 总消息数
    uint32_t routed_messages;       // 路由消息数
    uint32_t dropped_messages;      // 丢弃消息数
    uint32_t events_sent;           // 发送事件数
    uint32_t uptime_seconds;        // 运行时间
} agent_manager_stats_t;

// ==================== Agent管理器接口 ====================

/**
 * @brief 初始化Agent管理器
 * @param config 管理器配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_init(const agent_manager_config_t* config);

/**
 * @brief 反初始化Agent管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_deinit(void);

/**
 * @brief 启动Agent管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_start(void);

/**
 * @brief 停止Agent管理器
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_stop(void);

// ==================== Agent注册管理 ====================

/**
 * @brief 注册Agent类型
 * @param reg_info 注册信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_register_type(const agent_registration_info_t* reg_info);

/**
 * @brief 注销Agent类型
 * @param name Agent类型名称
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_unregister_type(const char* name);

/**
 * @brief 创建Agent实例
 * @param type_name Agent类型名称
 * @param config Agent配置，NULL使用默认配置
 * @return Agent句柄，NULL表示失败
 */
agent_handle_t agent_manager_create_agent(const char* type_name, const agent_config_t* config);

/**
 * @brief 销毁Agent实例
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_destroy_agent(agent_handle_t agent);

/**
 * @brief 注册Agent实例
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_register_agent(agent_handle_t agent);

/**
 * @brief 注销Agent实例
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_unregister_agent(agent_id_t agent_id);

// ==================== Agent查找和管理 ====================

/**
 * @brief 根据ID查找Agent
 * @param agent_id Agent ID
 * @return Agent句柄，NULL表示未找到
 */
agent_handle_t agent_manager_find_agent_by_id(agent_id_t agent_id);

/**
 * @brief 根据名称查找Agent
 * @param name Agent名称
 * @return Agent句柄，NULL表示未找到
 */
agent_handle_t agent_manager_find_agent_by_name(const char* name);

/**
 * @brief 根据类型查找Agent
 * @param type Agent类型
 * @param index 索引（同类型Agent的第几个）
 * @return Agent句柄，NULL表示未找到
 */
agent_handle_t agent_manager_find_agent_by_type(agent_type_t type, uint32_t index);

/**
 * @brief 获取Agent列表
 * @param type Agent类型，AGENT_TYPE_UNKNOWN表示所有类型
 * @param agents Agent列表缓冲区
 * @param max_count 最大Agent数量
 * @return 实际Agent数量
 */
uint32_t agent_manager_get_agents(agent_type_t type, agent_handle_t* agents, uint32_t max_count);

/**
 * @brief 获取Agent数量
 * @param type Agent类型，AGENT_TYPE_UNKNOWN表示所有类型
 * @return Agent数量
 */
uint32_t agent_manager_get_agent_count(agent_type_t type);

// ==================== Agent控制接口 ====================

/**
 * @brief 启动Agent
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_start_agent(agent_id_t agent_id);

/**
 * @brief 停止Agent
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_stop_agent(agent_id_t agent_id);

/**
 * @brief 重启Agent
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_restart_agent(agent_id_t agent_id);

/**
 * @brief 暂停Agent
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_pause_agent(agent_id_t agent_id);

/**
 * @brief 恢复Agent
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_resume_agent(agent_id_t agent_id);

/**
 * @brief 启动所有Agent
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_start_all_agents(void);

/**
 * @brief 停止所有Agent
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_stop_all_agents(void);

// ==================== 消息路由接口 ====================

/**
 * @brief 发送消息
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_send_message(const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 广播消息
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_broadcast_message(const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 发送消息给指定类型的Agent
 * @param type Agent类型
 * @param message 消息
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_send_message_to_type(agent_type_t type, const agent_message_t* message, uint32_t timeout_ms);

/**
 * @brief 发送命令给Agent
 * @param agent_id Agent ID
 * @param command 命令代码
 * @param data 命令数据
 * @param data_size 数据大小
 * @param timeout_ms 超时时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_send_command(agent_id_t agent_id, uint32_t command, const void* data, size_t data_size, uint32_t timeout_ms);

// ==================== 事件管理接口 ====================

/**
 * @brief 注册事件回调
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_register_event_callback(agent_manager_event_callback_t callback, void* user_data);

/**
 * @brief 注销事件回调
 * @param callback 回调函数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_unregister_event_callback(agent_manager_event_callback_t callback);

/**
 * @brief 发送事件
 * @param event_data 事件数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_send_event(const agent_event_data_t* event_data);

// ==================== 配置管理接口 ====================

/**
 * @brief 加载Agent配置
 * @param config_file 配置文件路径
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_load_config(const char* config_file);

/**
 * @brief 保存Agent配置
 * @param config_file 配置文件路径
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_save_config(const char* config_file);

/**
 * @brief 获取Agent配置
 * @param agent_id Agent ID
 * @param config 配置缓冲区
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_get_agent_config(agent_id_t agent_id, agent_config_t* config);

/**
 * @brief 设置Agent配置
 * @param agent_id Agent ID
 * @param config 新配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_set_agent_config(agent_id_t agent_id, const agent_config_t* config);

// ==================== 统计信息接口 ====================

/**
 * @brief 获取管理器统计信息
 * @param stats 统计信息缓冲区
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_get_stats(agent_manager_stats_t* stats);

/**
 * @brief 获取Agent统计信息
 * @param agent_id Agent ID
 * @param stats 统计信息缓冲区
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_get_agent_stats(agent_id_t agent_id, agent_stats_t* stats);

/**
 * @brief 重置统计信息
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_reset_stats(void);

/**
 * @brief 重置Agent统计信息
 * @param agent_id Agent ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t agent_manager_reset_agent_stats(agent_id_t agent_id);

// ==================== 调试和监控接口 ====================

/**
 * @brief 打印Agent信息
 * @param agent_id Agent ID，0表示所有Agent
 */
void agent_manager_print_agent_info(agent_id_t agent_id);

/**
 * @brief 打印统计信息
 */
void agent_manager_print_stats(void);

/**
 * @brief 检查Agent健康状态
 * @param agent_id Agent ID
 * @return true 健康，false 不健康
 */
bool agent_manager_check_agent_health(agent_id_t agent_id);

/**
 * @brief 获取系统状态
 * @return true 正常，false 异常
 */
bool agent_manager_get_system_status(void);

#ifdef __cplusplus
}
#endif

#endif // AGENT_MANAGER_H
