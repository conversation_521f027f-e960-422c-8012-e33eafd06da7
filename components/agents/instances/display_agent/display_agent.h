/**
 * @file display_agent.h
 * @brief 显示Agent接口定义
 * 
 * 显示Agent负责管理所有显示设备，处理显示相关的命令和事件
 */

#ifndef DISPLAY_AGENT_H
#define DISPLAY_AGENT_H

#include "agent_interface.h"
#include "hardware_types.h"
#include "display_hal.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 显示Agent常量定义 ====================

#define DISPLAY_AGENT_NAME          "display_agent"
#define DISPLAY_AGENT_VERSION       0x010000  // v1.0.0
#define DISPLAY_AGENT_MAX_DISPLAYS  4

// ==================== 显示Agent命令定义 ====================

/**
 * @brief 显示Agent命令枚举
 */
typedef enum {
    DISPLAY_CMD_INIT = 0x1000,          // 初始化显示设备
    DISPLAY_CMD_DEINIT,                 // 反初始化显示设备
    DISPLAY_CMD_POWER_ON,               // 开启显示设备
    DISPLAY_CMD_POWER_OFF,              // 关闭显示设备
    DISPLAY_CMD_CLEAR,                  // 清屏
    DISPLAY_CMD_SET_PIXEL,              // 设置像素
    DISPLAY_CMD_FILL_RECT,              // 填充矩形
    DISPLAY_CMD_DRAW_BITMAP,            // 绘制位图
    DISPLAY_CMD_FLUSH,                  // 刷新显示
    DISPLAY_CMD_SET_BRIGHTNESS,         // 设置亮度
    DISPLAY_CMD_SET_ROTATION,           // 设置旋转
    DISPLAY_CMD_SET_INVERT,             // 设置反转
    DISPLAY_CMD_GET_STATUS,             // 获取状态
    DISPLAY_CMD_GET_RESOLUTION,         // 获取分辨率
    
    // 眼睛显示特定命令
    DISPLAY_CMD_EYE_INIT,               // 初始化眼睛显示
    DISPLAY_CMD_EYE_BLINK,              // 眨眼
    DISPLAY_CMD_EYE_MOVE,               // 眼球移动
    DISPLAY_CMD_EYE_SET_EMOTION,        // 设置情绪
    DISPLAY_CMD_EYE_START_ANIMATION,    // 开始动画
    DISPLAY_CMD_EYE_STOP_ANIMATION,     // 停止动画
    
    // 配置命令
    DISPLAY_CMD_ADD_DEVICE,             // 添加显示设备
    DISPLAY_CMD_REMOVE_DEVICE,          // 移除显示设备
    DISPLAY_CMD_LIST_DEVICES,           // 列出设备
    DISPLAY_CMD_SET_DEFAULT_DEVICE      // 设置默认设备
} display_agent_command_t;

// ==================== 显示Agent事件定义 ====================

/**
 * @brief 显示Agent事件枚举
 */
typedef enum {
    DISPLAY_EVENT_DEVICE_ADDED = 0x2000,   // 设备添加
    DISPLAY_EVENT_DEVICE_REMOVED,          // 设备移除
    DISPLAY_EVENT_DEVICE_ERROR,            // 设备错误
    DISPLAY_EVENT_FLUSH_COMPLETE,          // 刷新完成
    DISPLAY_EVENT_ANIMATION_COMPLETE,      // 动画完成
    DISPLAY_EVENT_BRIGHTNESS_CHANGED,      // 亮度改变
    DISPLAY_EVENT_ROTATION_CHANGED         // 旋转改变
} display_agent_event_t;

// ==================== 显示Agent数据结构 ====================

/**
 * @brief 像素设置参数
 */
typedef struct {
    hardware_id_t device_id;
    uint16_t x;
    uint16_t y;
    uint32_t color;
} display_set_pixel_params_t;

/**
 * @brief 矩形填充参数
 */
typedef struct {
    hardware_id_t device_id;
    uint16_t x;
    uint16_t y;
    uint16_t width;
    uint16_t height;
    uint32_t color;
} display_fill_rect_params_t;

/**
 * @brief 位图绘制参数
 */
typedef struct {
    hardware_id_t device_id;
    uint16_t x;
    uint16_t y;
    uint16_t width;
    uint16_t height;
    const void* data;
    size_t data_size;
} display_draw_bitmap_params_t;

/**
 * @brief 刷新参数
 */
typedef struct {
    hardware_id_t device_id;
    display_area_t area;
    const void* data;
    size_t data_size;
} display_flush_params_t;

/**
 * @brief 亮度设置参数
 */
typedef struct {
    hardware_id_t device_id;
    uint8_t brightness;
} display_brightness_params_t;

/**
 * @brief 旋转设置参数
 */
typedef struct {
    hardware_id_t device_id;
    display_rotation_t rotation;
} display_rotation_params_t;

/**
 * @brief 眼球移动参数
 */
typedef struct {
    int16_t x_offset;
    int16_t y_offset;
    uint32_t duration_ms;
    bool smooth;
} display_eye_move_params_t;

/**
 * @brief 眼睛情绪参数
 */
typedef struct {
    uint32_t emotion;
    uint32_t duration_ms;
    bool auto_return;
} display_eye_emotion_params_t;

/**
 * @brief 显示Agent配置
 */
typedef struct {
    uint32_t max_devices;           // 最大设备数量
    hardware_id_t default_device;   // 默认设备ID
    bool auto_init_devices;         // 自动初始化设备
    bool enable_eye_display;        // 启用眼睛显示
    uint32_t animation_fps;         // 动画帧率
    uint32_t buffer_size;           // 缓冲区大小
} display_agent_config_t;

/**
 * @brief 显示Agent私有数据
 */
typedef struct {
    display_agent_config_t config;
    hal_device_t* devices[DISPLAY_AGENT_MAX_DISPLAYS];
    uint32_t device_count;
    hardware_id_t default_device_id;
    bool eye_display_initialized;
    bool animation_running;
    TaskHandle_t animation_task;
    void* eye_display_context;
} display_agent_private_t;

// ==================== 显示Agent接口 ====================

/**
 * @brief 获取显示Agent注册信息
 * @return 注册信息指针
 */
const agent_registration_info_t* display_agent_get_registration_info(void);

/**
 * @brief 创建显示Agent
 * @param config Agent配置，NULL使用默认配置
 * @return Agent句柄，NULL表示失败
 */
agent_handle_t display_agent_create(const agent_config_t* config);

/**
 * @brief 销毁显示Agent
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_destroy(agent_handle_t agent);

// ==================== 显示设备管理接口 ====================

/**
 * @brief 添加显示设备
 * @param agent Agent句柄
 * @param device_id 设备ID
 * @param device_config 设备配置
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_add_device(agent_handle_t agent, hardware_id_t device_id, const void* device_config);

/**
 * @brief 移除显示设备
 * @param agent Agent句柄
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_remove_device(agent_handle_t agent, hardware_id_t device_id);

/**
 * @brief 获取设备列表
 * @param agent Agent句柄
 * @param device_ids 设备ID列表
 * @param max_count 最大数量
 * @return 实际设备数量
 */
uint32_t display_agent_get_devices(agent_handle_t agent, hardware_id_t* device_ids, uint32_t max_count);

/**
 * @brief 设置默认设备
 * @param agent Agent句柄
 * @param device_id 设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_set_default_device(agent_handle_t agent, hardware_id_t device_id);

// ==================== 显示操作接口 ====================

/**
 * @brief 清屏
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param color 清屏颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_clear(agent_handle_t agent, hardware_id_t device_id, uint32_t color);

/**
 * @brief 设置像素
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param x X坐标
 * @param y Y坐标
 * @param color 像素颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_set_pixel(agent_handle_t agent, hardware_id_t device_id, uint16_t x, uint16_t y, uint32_t color);

/**
 * @brief 填充矩形
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param color 填充颜色
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_fill_rect(agent_handle_t agent, hardware_id_t device_id, uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint32_t color);

/**
 * @brief 绘制位图
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param data 位图数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_draw_bitmap(agent_handle_t agent, hardware_id_t device_id, uint16_t x, uint16_t y, uint16_t width, uint16_t height, const void* data);

/**
 * @brief 刷新显示
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param area 刷新区域，NULL表示全屏
 * @param data 显示数据
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_flush(agent_handle_t agent, hardware_id_t device_id, const display_area_t* area, const void* data);

// ==================== 显示配置接口 ====================

/**
 * @brief 设置亮度
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param brightness 亮度值 (0-255)
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_set_brightness(agent_handle_t agent, hardware_id_t device_id, uint8_t brightness);

/**
 * @brief 设置旋转角度
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param rotation 旋转角度
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_set_rotation(agent_handle_t agent, hardware_id_t device_id, display_rotation_t rotation);

/**
 * @brief 设置颜色反转
 * @param agent Agent句柄
 * @param device_id 设备ID，0表示默认设备
 * @param invert 是否反转
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_set_invert(agent_handle_t agent, hardware_id_t device_id, bool invert);

// ==================== 眼睛显示接口 ====================

/**
 * @brief 初始化眼睛显示
 * @param agent Agent句柄
 * @param left_device_id 左眼设备ID
 * @param right_device_id 右眼设备ID
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_init(agent_handle_t agent, hardware_id_t left_device_id, hardware_id_t right_device_id);

/**
 * @brief 眨眼
 * @param agent Agent句柄
 * @param duration_ms 持续时间
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_blink(agent_handle_t agent, uint32_t duration_ms);

/**
 * @brief 眼球移动
 * @param agent Agent句柄
 * @param x_offset X偏移
 * @param y_offset Y偏移
 * @param duration_ms 持续时间
 * @param smooth 是否平滑移动
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_move(agent_handle_t agent, int16_t x_offset, int16_t y_offset, uint32_t duration_ms, bool smooth);

/**
 * @brief 设置眼睛情绪
 * @param agent Agent句柄
 * @param emotion 情绪类型
 * @param duration_ms 持续时间
 * @param auto_return 是否自动返回
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_set_emotion(agent_handle_t agent, uint32_t emotion, uint32_t duration_ms, bool auto_return);

/**
 * @brief 开始眼睛动画
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_start_animation(agent_handle_t agent);

/**
 * @brief 停止眼睛动画
 * @param agent Agent句柄
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_eye_stop_animation(agent_handle_t agent);

// ==================== 状态查询接口 ====================

/**
 * @brief 获取设备状态
 * @param agent Agent句柄
 * @param device_id 设备ID
 * @param status 状态输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_get_device_status(agent_handle_t agent, hardware_id_t device_id, display_status_t* status);

/**
 * @brief 获取设备分辨率
 * @param agent Agent句柄
 * @param device_id 设备ID
 * @param width 宽度输出
 * @param height 高度输出
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t display_agent_get_device_resolution(agent_handle_t agent, hardware_id_t device_id, uint16_t* width, uint16_t* height);

#ifdef __cplusplus
}
#endif

#endif // DISPLAY_AGENT_H
