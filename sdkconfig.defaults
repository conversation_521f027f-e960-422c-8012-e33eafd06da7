# ESP32-MCP-HARDWARE 默认配置

# ==================== 基础系统配置 ====================

# 启用C++支持
CONFIG_COMPILER_CXX_EXCEPTIONS=y
CONFIG_COMPILER_CXX_RTTI=y

# 内存配置
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096

# 日志配置
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE=y

# ==================== WiFi配置 ====================

CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP_WIFI_STATIC_TX_BUFFER=y
CONFIG_ESP_WIFI_TX_BUFFER_TYPE=1
CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM=16
CONFIG_ESP_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP_WIFI_TX_BA_WIN=6
CONFIG_ESP_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP_WIFI_RX_BA_WIN=6

# ==================== LWIP配置 ====================

CONFIG_LWIP_TCP_SND_BUF_DEFAULT=65535
CONFIG_LWIP_TCP_WND_DEFAULT=65535
CONFIG_LWIP_TCP_RECVMBOX_SIZE=32
CONFIG_LWIP_UDP_RECVMBOX_SIZE=32
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=64

# ==================== WebSocket配置 ====================

CONFIG_WS_TRANSPORT=y
CONFIG_WS_BUFFER_SIZE=2048

# ==================== SPI配置 ====================

CONFIG_SPI_MASTER_IN_IRAM=y
CONFIG_SPI_MASTER_ISR_IN_IRAM=y

# ==================== LCD配置 ====================

CONFIG_LCD_PANEL_IO_FORMAT_BUF_SIZE=32

# ==================== FreeRTOS配置 ====================

CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3072
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1536

# ==================== 应用程序特定配置 ====================

# WiFi凭据
CONFIG_WIFI_SSID="Your_WiFi_SSID"
CONFIG_WIFI_PASSWORD="Your_WiFi_Password"

# MCP服务器端点
CONFIG_MCP_ENDPOINT="wss://api.xiaozhi.me/mcp/?token=your_token"

# 硬件引脚配置
CONFIG_PIN_SCK=19
CONFIG_PIN_MOSI=20
CONFIG_PIN_DC=21
CONFIG_PIN_RST=1
CONFIG_PIN_CS_LEFT=2
CONFIG_PIN_CS_RIGHT=45
CONFIG_LED_PIN=2

# LVGL配置
CONFIG_LV_COLOR_DEPTH_16=y
CONFIG_LV_COLOR_16_SWAP=y
CONFIG_LV_MEM_SIZE=32768
CONFIG_LV_DISP_DEF_REFR_PERIOD=30
CONFIG_LV_INDEV_DEF_READ_PERIOD=30

# 眼睛动画配置
CONFIG_EYE_BLINK_INTERVAL_MIN=3000
CONFIG_EYE_BLINK_INTERVAL_MAX=6000
CONFIG_EYE_LOOK_INTERVAL_MIN=4000
CONFIG_EYE_LOOK_INTERVAL_MAX=10000
CONFIG_EYE_EMOTION_INTERVAL_MIN=10000
CONFIG_EYE_EMOTION_INTERVAL_MAX=20000
