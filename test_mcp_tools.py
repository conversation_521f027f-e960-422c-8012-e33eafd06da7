#!/usr/bin/env python3
"""
ESP32-MCP-HARDWARE MCP工具测试脚本
用于测试MCP工具的功能
"""

import json
import asyncio
import websockets
import argparse
import sys

class MCPToolTester:
    def __init__(self, uri):
        self.uri = uri
        self.request_id = 1
    
    def create_request(self, method, params=None):
        """创建MCP请求"""
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        self.request_id += 1
        return request
    
    async def send_request(self, websocket, request):
        """发送请求并等待响应"""
        print(f"📤 发送请求: {json.dumps(request, indent=2, ensure_ascii=False)}")
        await websocket.send(json.dumps(request))
        
        response = await websocket.recv()
        response_data = json.loads(response)
        print(f"📥 收到响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        return response_data
    
    async def test_led_control(self, websocket):
        """测试LED控制工具"""
        print("\n🔸 测试LED控制工具")
        
        # 测试LED开启
        request = self.create_request("tools/call", {
            "name": "led_control",
            "arguments": {"state": "on"}
        })
        await self.send_request(websocket, request)
        
        await asyncio.sleep(2)
        
        # 测试LED闪烁
        request = self.create_request("tools/call", {
            "name": "led_control", 
            "arguments": {"state": "blink"}
        })
        await self.send_request(websocket, request)
        
        await asyncio.sleep(3)
        
        # 测试LED关闭
        request = self.create_request("tools/call", {
            "name": "led_control",
            "arguments": {"state": "off"}
        })
        await self.send_request(websocket, request)
    
    async def test_system_info(self, websocket):
        """测试系统信息工具"""
        print("\n🔸 测试系统信息工具")
        
        request = self.create_request("tools/call", {
            "name": "system_info",
            "arguments": {}
        })
        await self.send_request(websocket, request)
    
    async def test_eye_control(self, websocket):
        """测试眼睛控制工具"""
        print("\n🔸 测试眼睛控制工具")
        
        # 测试眨眼
        request = self.create_request("tools/call", {
            "name": "eye_control",
            "arguments": {
                "action": "blink"
            }
        })
        await self.send_request(websocket, request)
        
        await asyncio.sleep(2)
        
        # 测试眼球移动
        request = self.create_request("tools/call", {
            "name": "eye_control",
            "arguments": {
                "action": "look",
                "params": {"x": 10, "y": -5}
            }
        })
        await self.send_request(websocket, request)
        
        await asyncio.sleep(2)
        
        # 测试情绪切换
        request = self.create_request("tools/call", {
            "name": "eye_control",
            "arguments": {
                "action": "emotion",
                "params": {"emotion": "happy"}
            }
        })
        await self.send_request(websocket, request)
        
        await asyncio.sleep(3)
        
        # 恢复正常
        request = self.create_request("tools/call", {
            "name": "eye_control",
            "arguments": {
                "action": "emotion",
                "params": {"emotion": "normal"}
            }
        })
        await self.send_request(websocket, request)
    
    async def test_calculator(self, websocket):
        """测试计算器工具"""
        print("\n🔸 测试计算器工具")
        
        expressions = ["10+5", "20-3", "6*7", "24/4"]
        
        for expr in expressions:
            request = self.create_request("tools/call", {
                "name": "calculator",
                "arguments": {"expression": expr}
            })
            await self.send_request(websocket, request)
            await asyncio.sleep(1)
    
    async def test_tools_list(self, websocket):
        """测试工具列表"""
        print("\n🔸 获取工具列表")
        
        request = self.create_request("tools/list")
        await self.send_request(websocket, request)
    
    async def run_tests(self):
        """运行所有测试"""
        try:
            print(f"🔗 连接到 {self.uri}")
            async with websockets.connect(self.uri) as websocket:
                print("✅ WebSocket连接成功")
                
                # 获取工具列表
                await self.test_tools_list(websocket)
                
                # 测试系统信息
                await self.test_system_info(websocket)
                
                # 测试LED控制
                await self.test_led_control(websocket)
                
                # 测试眼睛控制
                await self.test_eye_control(websocket)
                
                # 测试计算器
                await self.test_calculator(websocket)
                
                print("\n🎉 所有测试完成")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="ESP32-MCP-HARDWARE MCP工具测试")
    parser.add_argument("--uri", default="ws://*************:8080", 
                       help="WebSocket URI (默认: ws://*************:8080)")
    parser.add_argument("--test", choices=["all", "led", "system", "eye", "calc"],
                       default="all", help="要运行的测试 (默认: all)")
    
    args = parser.parse_args()
    
    print("🚀 ESP32-MCP-HARDWARE MCP工具测试器")
    print(f"📡 目标URI: {args.uri}")
    print(f"🧪 测试类型: {args.test}")
    print("-" * 50)
    
    tester = MCPToolTester(args.uri)
    
    try:
        asyncio.run(tester.run_tests())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
