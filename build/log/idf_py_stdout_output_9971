[1/2] Generating ../../partition_table/partition-table.bin
FAILED: partition_table/partition-table.bin /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin 
cd /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/partition_table && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/gen_esp32part.py -q --offset 0x8000 --flash-size 2MB -- /Users/<USER>/code/esp32-mcp-hardware/partitions.csv /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -E echo "Partition table binary generated. Contents:" && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -E echo "*******************************************************************************" && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/gen_esp32part.py -q --offset 0x8000 --flash-size 2MB -- /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -E echo "*******************************************************************************"
Partitions tables occupies 3.1MB of flash (3211264 bytes) which does not fit in configured flash size 2MB. Change the flash size in menuconfig under the 'Serial Flasher Config' menu.
ninja: build stopped: subcommand failed.
