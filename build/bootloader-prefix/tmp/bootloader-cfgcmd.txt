cmd='/Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake;-DSDKCONFIG=/Users/<USER>/code/esp32-mcp-hardware/sdkconfig;-DIDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf;-DIDF_TARGET=esp32s3;-DPYTHON_DEPS_CHECKED=1;-DPYTHON=/Users/<USER>/.espressif/python_env/idf5.4_py3.13_env/bin/python;-DEXTRA_COMPONENT_DIRS=/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader;-DPROJECT_SOURCE_DIR=/Users/<USER>/code/esp32-mcp-hardware;-DIGNORE_EXTRA_COMPONENT=;-GNinja;-S;<SOURCE_DIR><SOURCE_SUBDIR>;-B;<BINARY_DIR>'
