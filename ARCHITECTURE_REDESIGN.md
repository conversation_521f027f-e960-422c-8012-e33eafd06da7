# ESP32-MCP-HARDWARE 架构重构设计

## 🎯 重构目标

将现有的单一硬件项目重构为支持多硬件和多Agent模式的可扩展架构，实现：

1. **硬件抽象化** - 支持不同类型的硬件设备
2. **Agent模块化** - 支持多个独立的Agent实例
3. **插件化架构** - 工具和功能模块可动态加载
4. **分层配置管理** - 支持不同层级的配置
5. **通信协议抽象** - 支持多种通信协议

## 📁 新项目结构设计

```
ESP32-MCP-HARDWARE/
├── CMakeLists.txt                    # 主构建文件
├── sdkconfig.defaults                # 默认配置
├── build.sh                          # 构建脚本
├── README.md                         # 项目文档
├── ARCHITECTURE_REDESIGN.md          # 架构重构设计文档
├── PROJECT_SUMMARY.md                # 项目总结
├── VERIFICATION_CHECKLIST.md         # 验证清单
│
├── main/                             # 主程序目录
│   ├── CMakeLists.txt                # main组件构建文件
│   ├── main.c                        # 主程序入口
│   └── include/
│       └── main_config.h             # 主程序配置
│
├── components/                       # 组件目录
│   │
│   ├── core/                         # 核心服务层
│   │   ├── CMakeLists.txt
│   │   ├── include/
│   │   │   ├── system_manager.h      # 系统管理器
│   │   │   ├── task_scheduler.h      # 任务调度器
│   │   │   ├── event_manager.h       # 事件管理器
│   │   │   ├── logger.h              # 日志系统
│   │   │   └── error_handler.h       # 错误处理
│   │   └── src/
│   │       ├── system_manager.c
│   │       ├── task_scheduler.c
│   │       ├── event_manager.c
│   │       ├── logger.c
│   │       └── error_handler.c
│   │
│   ├── config/                       # 配置管理系统
│   │   ├── CMakeLists.txt
│   │   ├── Kconfig.projbuild         # menuconfig配置
│   │   ├── include/
│   │   │   ├── config_manager.h      # 配置管理器
│   │   │   ├── global_config.h       # 全局配置
│   │   │   ├── hardware_config.h     # 硬件配置
│   │   │   └── agent_config.h        # Agent配置
│   │   └── src/
│   │       ├── config_manager.c
│   │       ├── global_config.c
│   │       ├── hardware_config.c
│   │       └── agent_config.c
│   │
│   ├── hardware/                     # 硬件抽象层(HAL)
│   │   ├── CMakeLists.txt
│   │   ├── include/
│   │   │   ├── hardware_manager.h    # 硬件管理器
│   │   │   ├── hal_interface.h       # HAL接口定义
│   │   │   └── hardware_types.h      # 硬件类型定义
│   │   ├── display/                  # 显示设备
│   │   │   ├── include/
│   │   │   │   ├── display_hal.h     # 显示HAL接口
│   │   │   │   ├── gc9a01_driver.h   # GC9A01驱动
│   │   │   │   └── lvgl_adapter.h    # LVGL适配器
│   │   │   └── src/
│   │   │       ├── display_manager.c
│   │   │       ├── gc9a01_driver.c
│   │   │       ├── lvgl_adapter.c
│   │   │       └── eye_display.c     # 眼睛显示实现
│   │   ├── sensors/                  # 传感器设备
│   │   │   ├── include/
│   │   │   │   ├── sensor_hal.h      # 传感器HAL接口
│   │   │   │   ├── temperature_sensor.h
│   │   │   │   ├── light_sensor.h
│   │   │   │   └── motion_sensor.h
│   │   │   └── src/
│   │   │       ├── sensor_manager.c
│   │   │       ├── temperature_sensor.c
│   │   │       ├── light_sensor.c
│   │   │       └── motion_sensor.c
│   │   ├── actuators/                # 执行器设备
│   │   │   ├── include/
│   │   │   │   ├── actuator_hal.h    # 执行器HAL接口
│   │   │   │   ├── led_controller.h  # LED控制器
│   │   │   │   ├── servo_controller.h # 舵机控制器
│   │   │   │   └── buzzer_controller.h # 蜂鸣器控制器
│   │   │   └── src/
│   │   │       ├── actuator_manager.c
│   │   │       ├── led_controller.c
│   │   │       ├── servo_controller.c
│   │   │       └── buzzer_controller.c
│   │   └── connectivity/             # 连接设备
│   │       ├── include/
│   │       │   ├── connectivity_hal.h # 连接HAL接口
│   │       │   ├── wifi_manager.h    # WiFi管理器
│   │       │   ├── bluetooth_manager.h # 蓝牙管理器
│   │       │   └── ethernet_manager.h # 以太网管理器
│   │       └── src/
│   │           ├── connectivity_manager.c
│   │           ├── wifi_manager.c
│   │           ├── bluetooth_manager.c
│   │           └── ethernet_manager.c
│   │
│   ├── communication/                # 通信层
│   │   ├── CMakeLists.txt
│   │   ├── include/
│   │   │   ├── comm_manager.h        # 通信管理器
│   │   │   ├── protocol_interface.h  # 协议接口
│   │   │   ├── mcp_protocol.h        # MCP协议
│   │   │   ├── mqtt_protocol.h       # MQTT协议
│   │   │   └── http_protocol.h       # HTTP协议
│   │   └── src/
│   │       ├── comm_manager.c
│   │       ├── mcp_client.c          # 重构的MCP客户端
│   │       ├── mqtt_client.c
│   │       └── http_client.c
│   │
│   ├── agents/                       # Agent管理系统
│   │   ├── CMakeLists.txt
│   │   ├── include/
│   │   │   ├── agent_manager.h       # Agent管理器
│   │   │   ├── agent_interface.h     # Agent接口
│   │   │   ├── message_router.h      # 消息路由器
│   │   │   └── agent_registry.h      # Agent注册表
│   │   ├── core/                     # Agent核心
│   │   │   ├── agent_base.c          # Agent基类
│   │   │   ├── agent_manager.c
│   │   │   ├── message_router.c
│   │   │   └── agent_registry.c
│   │   └── instances/                # Agent实例
│   │       ├── display_agent/        # 显示Agent
│   │       │   ├── display_agent.h
│   │       │   └── display_agent.c
│   │       ├── sensor_agent/         # 传感器Agent
│   │       │   ├── sensor_agent.h
│   │       │   └── sensor_agent.c
│   │       ├── control_agent/        # 控制Agent
│   │       │   ├── control_agent.h
│   │       │   └── control_agent.c
│   │       └── system_agent/         # 系统Agent
│   │           ├── system_agent.h
│   │           └── system_agent.c
│   │
│   └── tools/                        # 工具系统
│       ├── CMakeLists.txt
│       ├── include/
│       │   ├── tool_manager.h        # 工具管理器
│       │   ├── tool_interface.h      # 工具接口
│       │   └── tool_registry.h       # 工具注册表
│       ├── core/                     # 工具核心
│       │   ├── tool_manager.c
│       │   ├── tool_registry.c
│       │   └── tool_loader.c         # 工具加载器
│       └── plugins/                  # 工具插件
│           ├── led_tool/             # LED控制工具
│           │   ├── led_tool.h
│           │   └── led_tool.c
│           ├── system_info_tool/     # 系统信息工具
│           │   ├── system_info_tool.h
│           │   └── system_info_tool.c
│           ├── eye_control_tool/     # 眼睛控制工具
│           │   ├── eye_control_tool.h
│           │   └── eye_control_tool.c
│           ├── calculator_tool/      # 计算器工具
│           │   ├── calculator_tool.h
│           │   └── calculator_tool.c
│           └── sensor_tool/          # 传感器工具
│               ├── sensor_tool.h
│               └── sensor_tool.c
│
├── examples/                         # 示例配置
│   ├── basic_eye_display/            # 基础眼睛显示示例
│   │   ├── README.md
│   │   ├── sdkconfig.example
│   │   └── config.json
│   ├── multi_sensor_system/          # 多传感器系统示例
│   │   ├── README.md
│   │   ├── sdkconfig.example
│   │   └── config.json
│   ├── smart_home_hub/               # 智能家居中心示例
│   │   ├── README.md
│   │   ├── sdkconfig.example
│   │   └── config.json
│   └── industrial_monitor/           # 工业监控示例
│       ├── README.md
│       ├── sdkconfig.example
│       └── config.json
│
├── docs/                             # 文档目录
│   ├── architecture.md              # 架构文档
│   ├── hardware_guide.md            # 硬件指南
│   ├── agent_development.md         # Agent开发指南
│   ├── tool_development.md          # 工具开发指南
│   └── api_reference.md             # API参考
│
└── scripts/                         # 脚本目录
    ├── build_all.sh                 # 构建所有示例
    ├── flash_example.sh             # 烧录示例
    └── generate_config.py           # 配置生成器
```

## 🏗️ 架构设计原则

### 1. 分层架构
- **应用层**: Agent实例和工具插件
- **服务层**: Agent管理、工具管理、通信管理
- **抽象层**: 硬件抽象层(HAL)
- **驱动层**: 具体硬件驱动

### 2. 模块化设计
- 每个功能模块独立编译
- 清晰的接口定义
- 松耦合设计

### 3. 插件化架构
- 工具系统支持动态加载
- Agent实例可配置启用/禁用
- 硬件驱动可选择性编译

### 4. 配置驱动
- 通过配置文件控制系统行为
- 支持运行时配置修改
- 分层配置管理

## 🔄 重构实施计划

### 阶段1: 核心基础设施
1. 创建新的目录结构
2. 实现核心服务层
3. 实现配置管理系统

### 阶段2: 硬件抽象层
1. 设计HAL接口
2. 重构现有硬件驱动
3. 实现硬件管理器

### 阶段3: Agent系统
1. 设计Agent接口
2. 实现Agent管理器
3. 重构现有功能为Agent

### 阶段4: 通信和工具
1. 重构MCP通信层
2. 实现工具插件系统
3. 迁移现有工具

### 阶段5: 示例和文档
1. 创建示例配置
2. 更新文档
3. 测试验证

## 🎯 预期收益

1. **可扩展性**: 轻松添加新硬件和Agent
2. **可维护性**: 模块化设计便于维护
3. **可重用性**: 组件可在不同项目中重用
4. **灵活性**: 配置驱动的系统行为
5. **标准化**: 统一的接口和规范
